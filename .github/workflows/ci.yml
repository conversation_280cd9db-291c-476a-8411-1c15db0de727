name: CI Workflow
on:
  # Run this workflow every time a new commit pushed to upstream/fork repository.
  # Run workflow on fork repository will help contributors find and resolve issues before sending a PR.
  push:
  pull_request:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.actor }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  golangci:
    name: lint
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v3
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: vendor
        run: hack/verify-vendor.sh
      - name: lint
        run: hack/verify-staticcheck.sh
      - name: import alias
        run: hack/verify-import-aliases.sh
  codegen:
    name: codegen
    runs-on: ubuntu-22.04
    env:
      GOPATH: ${{ github.workspace }}
    defaults:
      run:
        working-directory: ${{ env.GOPATH }}/src/github.com/karmada-io/karmada
    steps:
      - name: checkout code
        uses: actions/checkout@v3
        with:
          path: ${{ env.GOPATH }}/src/github.com/karmada-io/karmada
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: Install Protoc
        uses: arduino/setup-protoc@v1
        with:
          version: '3.x'
          # Use the automatic token, so that this task can be run in the forked repo.
          # https://docs.github.com/en/actions/security-guides/automatic-token-authentication
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: verify codegen
        run: hack/verify-codegen.sh
      - name: verify crdgen
        run: hack/verify-crdgen.sh
      - name: verify protobuf
        run: hack/verify-estimator-protobuf.sh
      - name: verify swagger docs
        run: hack/verify-swagger-docs.sh
      - name: verify lifted docs
        run: hack/verify-lifted.sh
  build:
    name: compile
    needs: codegen # rely on codegen successful completion
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v3
        with:
          # Number of commits to fetch. 0 indicates all history for all branches and tags.
          # We need to guess version via git tags.
          fetch-depth: 0
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: compile
        run: make all
  test:
    name: unit test
    needs: build
    runs-on: ubuntu-22.04
    env:
      COLOR_GOTEST_ENABLED: enabled
    steps:
      - name: checkout code
        uses: actions/checkout@v3
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: make test
        run: make test
      - name: Upload coverage to Codecov
        # Prevent running from the forked repository that doesn't need to upload coverage.
        # In addition, running on the forked repository would fail as missing the necessary secret.
        if: ${{ github.repository == 'karmada-io/karmada' }}
        uses: codecov/codecov-action@v3
        with:
          # Even though token upload token is not required for public repos,
          # but adding a token might increase successful uploads as per:
          # https://community.codecov.com/t/upload-issues-unable-to-locate-build-via-github-actions-api/3954
          token: ${{secrets.CODECOV_UPLOAD_TOKEN}}
          files: ./_output/coverage/coverage_pkg.txt,./_output/coverage/coverage_cmd.txt,./_output/coverage/coverage_examples.txt
          flags: unittests
          fail_ci_if_error: false
          verbose: true
  e2e:
    name: e2e test
    needs: build
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        # Here support the latest three minor releases of Kubernetes, this can be considered to be roughly
        # the same as the End of Life of the Kubernetes release: https://kubernetes.io/releases/
        # Please remember to update the CI Schedule Workflow when we add a new version.
        k8s: [ v1.25.0, v1.26.0, v1.27.3 ]
    steps:
      # Free up disk space on Ubuntu
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed, if set to "true" but frees about 6 GB
          tool-cache: false
          # all of these default to true, but feel free to set to "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: false
          docker-images: false
          swap-storage: false
      - name: checkout code
        uses: actions/checkout@v3
        with:
          # Number of commits to fetch. 0 indicates all history for all branches and tags.
          # We need to guess version via git tags.
          fetch-depth: 0
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: setup e2e test environment
        run: |
          export CLUSTER_VERSION=kindest/node:${{ matrix.k8s }}
          hack/local-up-karmada.sh
      - name: run e2e
        run: |
          export ARTIFACTS_PATH=${{ github.workspace }}/karmada-e2e-logs/${{ matrix.k8s }}/
          hack/run-e2e.sh
      - name: upload logs
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: karmada_e2e_log_${{ matrix.k8s }}
          path: ${{ github.workspace }}/karmada-e2e-logs/${{ matrix.k8s }}/
      - name: upload kind logs
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: karmada_kind_log_${{ matrix.k8s }}
          path: /tmp/karmada/
