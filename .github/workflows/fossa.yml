name: FOSSA
on:
  push:
jobs:
  fossa:
    name: FOSSA
    # prevent job running from forked repository, otherwise
    # 1. running on the forked repository would fail as missing necessary secret.
    # 2. running on the forked repository would use unnecessary GitHub Action time.
    if: ${{ github.repository == 'karmada-io/karmada' }}
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v3
      - name: Run FOSSA scan and upload build data
        uses: fossas/fossa-action@v1
        with:
          api-key: ${{secrets.FOSSA_API_KEY}}
