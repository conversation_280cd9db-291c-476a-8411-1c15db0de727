name: released image to <PERSON><PERSON>
on:
  release:
    types:
      - published
jobs:
  release-image:
    name: release images
    # prevent job running from forked repository, otherwise
    # 1. running on the forked repository would fail as missing necessary secret.
    # 2. running on the forked repository would use unnecessary GitHub Action time.
    if: ${{ github.repository == 'karmada-io/karmada' }}
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: build images
        env:
          REGISTRY: ${{secrets.SWR_REGISTRY}}
          REGISTRY_USER_NAME: ${{secrets.SWR_REGISTRY_USER_NAME}}
          REGISTRY_PASSWORD: ${{secrets.SWR_REGISTRY_PASSWORD}}
          REGISTRY_SERVER_ADDRESS: ${{secrets.SWR_REGISTRY_SERVER_ADDRESS}}
          VERSION: ${{ github.ref_name }}
        run: make upload-images
