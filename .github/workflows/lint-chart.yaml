# validate any chart changes under charts directory
name: Chart Lint

env:
  HELM_VERSION: v3.11.2
  KIND_VERSION: v0.14.0
  KIND_NODE_IMAGE: kindest/node:v1.27.3
  K8S_VERSION: v1.26.4

on:
  push:
  pull_request:
    paths:
      - "charts/**"

jobs:
  chart-lint-test:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: ${{ env.HELM_VERSION }}

      # Python is required because `ct lint` runs Yamale (https://github.com/23andMe/Yamale) and
      # yamllint (https://github.com/adrienverge/yamllint) which require Python
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.9
          check-latest: true

      - name: Set up chart-testing
        uses: helm/chart-testing-action@v2.4.0

      - name: Add dependency chart repos
        run: |
          helm repo add bitnami https://charts.bitnami.com/bitnami

      - name: Run chart-testing (list-changed)
        id: list-changed
        run: |
          changed=$( ct list-changed )
          if [[ -n "$changed" ]]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Run chart-testing (lint)
        if: steps.list-changed.outputs.changed == 'true'
        run: ct lint --debug --check-version-increment=false

      - name: Create kind cluster
        uses: helm/kind-action@v1.7.0
        if: steps.list-changed.outputs.changed == 'true'
        with:
          wait: 120s
          version: ${{ env.KIND_VERSION }}
          node_image: ${{ env.KIND_NODE_IMAGE }}
          kubectl_version: ${{ env.K8S_VERSION }}

      - name: Run chart-testing (install)
        if: steps.list-changed.outputs.changed == 'true'
        run: ct install --debug --helm-extra-args "--timeout 800s"
