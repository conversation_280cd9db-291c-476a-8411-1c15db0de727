# Design Document

## Overview

This design document outlines the refactoring of the PVC binding controller to use TypedWork resources instead of directly manipulating member cluster objects. The refactoring will align the PVC controller with Karmada's standard architecture pattern, following the same approach used by the binding controller.

The current implementation directly creates, updates, and deletes PVC resources in member clusters. The new design will create TypedWork resources that are then processed by the TypedWork execution controller, providing better consistency, monitoring, and debugging capabilities.

## Architecture

### Current Architecture

```
PVC with binding cluster label → PvcBindingController → Direct member cluster PVC operations
```

### New Architecture

```
PVC with binding cluster label → PvcBindingController → TypedWork → TypedWorkExecutionController → Member cluster PVC operations
```

### Key Components

1. **PvcBindingController** (Refactored)
   - Watches PVC resources with binding cluster labels
   - Creates/updates/deletes TypedWork resources instead of direct member cluster operations
   - Handles PV deletion logic based on reclaim policies
   - Manages finalizers and cleanup

2. **TypedWork Resources**
   - Intermediate representation of PVC operations
   - Contains PVC manifest and target cluster information
   - Processed by existing TypedWorkExecutionController

3. **TypedWorkExecutionController** (Existing)
   - Processes TypedWork resources
   - Performs actual member cluster operations
   - Provides status feedback and error handling

## Components and Interfaces

### PvcBindingController Changes

#### Core Methods Refactoring

1. **syncPVCToCluster** → **syncPVCToTypedWork**
   - Creates TypedWork instead of direct cluster operations
   - Uses helper functions from `pkg/util/helper/typedwork.go`
   - Maintains same external behavior

2. **syncPVCToMemberCluster** → **createOrUpdateTypedWork**
   - Leverages `CreateOrUpdateTypedWork` helper function
   - Handles TypedWork metadata generation
   - Manages labels and annotations for proper tracking

3. **deletePVCFromCluster** → **deleteTypedWork**
   - Deletes TypedWork resources instead of direct member cluster operations
   - Maintains PV deletion logic for reclaim policies
   - Handles cleanup of orphaned TypedWork resources

4. **cleanupOtherMemberClusters** → **cleanupOrphanTypedWorks**
   - Finds and removes TypedWork resources for non-target clusters
   - Uses TypedWork query functions from helper package

#### New Helper Functions

The following functions will be added to `pkg/util/helper/typedwork.go`:

1. **CreateOrUpdatePVCTypedWork**
   ```go
   func CreateOrUpdatePVCTypedWork(client client.Client, pvc *corev1.PersistentVolumeClaim, clusterName string) error
   ```
   - Creates TypedWork for PVC resources
   - Handles PVC-specific metadata and annotations
   - Generates appropriate TypedWork name and namespace

2. **GetPVCTypedWorks**
   ```go
   func GetPVCTypedWorks(client client.Client, pvcNamespace, pvcName string) (*workv1alpha1.TypedWorkList, error)
   ```
   - Retrieves TypedWork resources for a specific PVC
   - Uses label selectors for efficient querying

3. **DeletePVCTypedWork**
   ```go
   func DeletePVCTypedWork(client client.Client, pvcNamespace, pvcName, clusterName string) error
   ```
   - Deletes specific TypedWork for PVC in target cluster
   - Handles graceful deletion with proper error handling

### TypedWork Resource Structure

TypedWork resources for PVCs will follow this structure:

```yaml
apiVersion: work.karmada.io/v1alpha1
kind: TypedWork
metadata:
  name: pvc-<pvc-name>-<hash>
  namespace: karmada-es-<cluster-name>
  labels:
    work.karmada.io/resource-type: "persistentvolumeclaim"
    work.karmada.io/pvc-name: "<pvc-name>"
    work.karmada.io/pvc-namespace: "<pvc-namespace>"
  annotations:
    work.karmada.io/pvc-binding-cluster: "<cluster-name>"
  finalizers:
    - karmada.io/execution-controller
spec:
  objectReference:
    apiVersion: v1
    kind: PersistentVolumeClaim
    namespace: <pvc-namespace>
    name: <pvc-name>
    uid: <pvc-uid>
  workload:
    manifest:
      # PVC manifest JSON
```

## Data Models

### PVC Metadata Extensions

The PVC controller will use the following labels and annotations for TypedWork management:

#### Labels
- `work.karmada.io/resource-type: "persistentvolumeclaim"` - Identifies PVC TypedWork resources
- `work.karmada.io/pvc-name: "<pvc-name>"` - PVC name for querying
- `work.karmada.io/pvc-namespace: "<pvc-namespace>"` - PVC namespace for querying

#### Annotations
- `work.karmada.io/pvc-binding-cluster: "<cluster-name>"` - Target cluster information
- `work.karmada.io/resource-template-uid: "<pvc-uid>"` - Original PVC UID for tracking

### TypedWork Naming Convention

TypedWork resources for PVCs will use the following naming pattern:
- **Name**: `pvc-<pvc-name>-<hash>` where hash is generated from PVC namespace and name
- **Namespace**: `karmada-es-<cluster-name>` (execution space for target cluster)

## Error Handling

### Error Propagation

1. **TypedWork Creation Errors**
   - Logged with appropriate context
   - Recorded as events on the original PVC
   - Controller requeues for retry

2. **TypedWork Status Monitoring**
   - Monitor TypedWork status conditions
   - Propagate errors back to PVC events
   - Handle TypedWork execution failures

3. **Cleanup Errors**
   - Graceful handling of orphaned TypedWork resources
   - Proper finalizer management
   - Retry logic for transient failures

### Backward Compatibility

1. **Migration Handling**
   - Detect existing direct member cluster PVCs
   - Gracefully transition to TypedWork-based approach
   - Maintain existing PV relationships

2. **Rollback Support**
   - Ensure TypedWork deletion doesn't break existing PVCs
   - Maintain PV reclaim policy handling
   - Preserve existing finalizer behavior

## Testing Strategy

### Unit Tests

1. **Controller Logic Tests**
   - Test TypedWork creation from PVC resources
   - Verify proper metadata and label assignment
   - Test error handling and retry logic

2. **Helper Function Tests**
   - Test `CreateOrUpdatePVCTypedWork` function
   - Verify TypedWork querying functions
   - Test cleanup and deletion functions

3. **Integration Tests**
   - Test end-to-end PVC to TypedWork flow
   - Verify TypedWork execution results in correct member cluster state
   - Test PV deletion scenarios with different reclaim policies

### Compatibility Tests

1. **Migration Tests**
   - Test transition from direct manipulation to TypedWork
   - Verify no data loss during migration
   - Test mixed scenarios (some PVCs using old approach, some using new)

2. **Backward Compatibility Tests**
   - Ensure existing PVC resources continue to work
   - Test PV relationships are maintained
   - Verify cleanup behavior remains consistent

### Performance Tests

1. **Scale Tests**
   - Test with large numbers of PVCs
   - Verify TypedWork creation performance
   - Monitor resource usage and memory consumption

2. **Latency Tests**
   - Compare latency between old and new approaches
   - Measure TypedWork processing time
   - Verify no significant performance regression

## Implementation Phases

### Phase 1: Helper Functions and Core Infrastructure
- Implement TypedWork helper functions in `pkg/util/helper/typedwork.go`
- Add PVC-specific TypedWork creation and management functions
- Create unit tests for helper functions

### Phase 2: Controller Refactoring
- Refactor `syncPVCToCluster` to use TypedWork
- Update deletion logic to work with TypedWork
- Implement cleanup functions for orphaned TypedWork resources

### Phase 3: Integration and Testing
- Integration testing with TypedWork execution controller
- End-to-end testing of PVC operations
- Performance and compatibility testing

### Phase 4: Migration and Cleanup
- Handle migration from existing direct manipulation approach
- Clean up deprecated code paths
- Documentation updates

## Monitoring and Observability

### Metrics
- TypedWork creation/update/deletion counts for PVCs
- Error rates and retry counts
- Processing latency metrics

### Logging
- Structured logging for TypedWork operations
- Debug information for troubleshooting
- Error context for failed operations

### Events
- Success events for TypedWork operations
- Warning events for failures and retries
- Information events for migration activities

## Security Considerations

### RBAC Requirements
- Ensure controller has appropriate permissions for TypedWork resources
- Maintain existing PVC and PV permissions
- Verify cluster-scoped resource access

### Resource Isolation
- Proper namespace isolation for TypedWork resources
- Secure handling of PVC manifests in TypedWork
- Protection against unauthorized TypedWork manipulation

## Performance Considerations

### Resource Usage
- Monitor memory usage for TypedWork resources
- Optimize TypedWork querying and caching
- Efficient cleanup of completed TypedWork resources

### Scalability
- Handle large numbers of PVCs efficiently
- Optimize TypedWork creation batching
- Consider rate limiting for TypedWork operations

### Caching Strategy
- Leverage existing informer caches for TypedWork resources
- Optimize label selector queries
- Minimize API server load