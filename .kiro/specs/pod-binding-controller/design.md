# Design Document

## Overview

This design document outlines the implementation of a new `pod_binding_controller` that creates TypedWork resources instead of directly manipulating member cluster pod objects. The new controller will follow Karmada's standard architecture pattern, similar to the binding controller and the refactored PVC binding controller.

The current `pod_execution_controller.go` directly creates, updates, and deletes pod resources in member clusters. The new design will create TypedWork resources that are then processed by the TypedWork execution controller, providing better consistency, monitoring, and debugging capabilities while maintaining the same external behavior.

## Architecture

### Current Architecture (pod_execution_controller.go)

```
Virtual Pod with cluster label → PodExecutionController → Direct member cluster pod operations
```

### New Architecture (pod_binding_controller.go)

```
Virtual Pod with cluster label → PodBindingController → TypedWork → TypedWorkExecutionController → Member cluster pod operations
```

### Key Components

1. **PodBindingController** (New)
   - Watches virtual pod resources with member cluster labels
   - Creates/updates/deletes TypedWork resources instead of direct member cluster operations
   - Handles PVC dependency synchronization
   - Manages finalizers and cleanup
   - Records events and handles errors

2. **TypedWork Resources**
   - Intermediate representation of pod operations
   - Contains pod manifest and target cluster information
   - Processed by existing TypedWorkExecutionController

3. **TypedWorkExecutionController** (Existing)
   - Processes TypedWork resources
   - Performs actual member cluster operations
   - Provides status feedback and error handling

4. **PVC Dependency Management**
   - Ensures PVC TypedWork resources exist before creating pod TypedWork
   - Coordinates with PVC binding controller
   - Handles cleanup ordering

## Components and Interfaces

### PodBindingController Structure

```go
type PodBindingController struct {
    client.Client
    EventRecorder           record.EventRecorder
    PredicateFunc           predicate.Predicate
    RatelimiterOptions      ratelimiterflag.Options
    ClusterLister           clusterlisters.ClusterLister
    ClusterClientSetFunc    func(string, client.Client, *util.ClientOption) (*util.ClusterClient, error)
    ClusterClientOption     *util.ClientOption
}
```

### Core Methods

#### 1. Reconcile Method
```go
func (c *PodBindingController) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error)
```
- Main reconciliation logic
- Gets virtual pod and validates cluster assignment
- Checks cluster readiness
- Delegates to syncPodToTypedWork

#### 2. syncPodToTypedWork Method
```go
func (c *PodBindingController) syncPodToTypedWork(ctx context.Context, pod *corev1.Pod, clusterName string) (controllerruntime.Result, error)
```
- Handles pod deletion vs creation/update logic
- Manages finalizers
- Coordinates with PVC dependencies
- Creates/updates/deletes TypedWork resources

#### 3. createOrUpdateTypedWork Method
```go
func (c *PodBindingController) createOrUpdateTypedWork(ctx context.Context, pod *corev1.Pod, clusterName string) error
```
- Creates TypedWork resource for pod
- Uses helper functions for TypedWork creation
- Handles pod-specific metadata and annotations
- Manages cleanup of orphaned TypedWork resources

#### 4. deleteTypedWork Method
```go
func (c *PodBindingController) deleteTypedWork(ctx context.Context, pod *corev1.Pod, clusterName string) (bool, error)
```
- Deletes TypedWork resource for pod
- Handles graceful deletion
- Returns whether deletion is complete

#### 5. syncPodPVCDependencies Method
```go
func (c *PodBindingController) syncPodPVCDependencies(ctx context.Context, pod *corev1.Pod, clusterName string) error
```
- Ensures PVC TypedWork resources exist before pod creation
- Coordinates with PVC binding controller
- Handles PVC dependency validation

#### 6. cleanupOrphanTypedWorks Method
```go
func (c *PodBindingController) cleanupOrphanTypedWorks(ctx context.Context, pod *corev1.Pod, targetCluster string) error
```
- Removes TypedWork resources from non-target clusters
- Handles cluster migration scenarios
- Ensures resource cleanup

### Helper Functions

The following functions will be added to `pkg/util/helper/typedwork.go`:

#### 1. CreateOrUpdatePodTypedWork
```go
func CreateOrUpdatePodTypedWork(client client.Client, pod *corev1.Pod, clusterName string) error
```
- Creates TypedWork for pod resources
- Handles pod-specific metadata and annotations
- Generates appropriate TypedWork name and namespace
- Manages pod manifest preparation

#### 2. GetPodTypedWorks
```go
func GetPodTypedWorks(client client.Client, podNamespace, podName string) (*workv1alpha1.TypedWorkList, error)
```
- Retrieves TypedWork resources for a specific pod
- Uses label selectors for efficient querying
- Returns all TypedWork resources across clusters

#### 3. DeletePodTypedWork
```go
func DeletePodTypedWork(client client.Client, podNamespace, podName, clusterName string) error
```
- Deletes specific TypedWork for pod in target cluster
- Handles graceful deletion with proper error handling
- Manages finalizer cleanup

#### 4. GetMemberPodForTypedWork
```go
func GetMemberPodForTypedWork(pod *corev1.Pod, clusterName string) *corev1.Pod
```
- Prepares pod manifest for member cluster deployment
- Removes virtual kubelet specific labels/annotations
- Adds cluster-specific metadata
- Handles resource transformations

### TypedWork Resource Structure

TypedWork resources for pods will follow this structure:

```yaml
apiVersion: work.karmada.io/v1alpha1
kind: TypedWork
metadata:
  name: pod-<pod-name>-<hash>
  namespace: karmada-es-<cluster-name>
  labels:
    work.karmada.io/resource-type: "pod"
    work.karmada.io/pod-name: "<pod-name>"
    work.karmada.io/pod-namespace: "<pod-namespace>"
    karmada.io/managed: "karmada"
  annotations:
    work.karmada.io/pod-binding-cluster: "<cluster-name>"
    work.karmada.io/resource-template-uid: "<pod-uid>"
  finalizers:
    - karmada.io/execution-controller
spec:
  objectReference:
    apiVersion: v1
    kind: Pod
    namespace: <pod-namespace>
    name: <pod-name>
    uid: <pod-uid>
  workload:
    manifest:
      # Pod manifest JSON (prepared for member cluster)
```

## Data Models

### Pod Metadata Extensions

The pod binding controller will use the following labels and annotations for TypedWork management:

#### Labels
- `work.karmada.io/resource-type: "pod"` - Identifies pod TypedWork resources
- `work.karmada.io/pod-name: "<pod-name>"` - Pod name for querying
- `work.karmada.io/pod-namespace: "<pod-namespace>"` - Pod namespace for querying
- `karmada.io/managed: "karmada"` - Indicates Karmada management

#### Annotations
- `work.karmada.io/pod-binding-cluster: "<cluster-name>"` - Target cluster information
- `work.karmada.io/resource-template-uid: "<pod-uid>"` - Original pod UID for tracking
- `work.karmada.io/pvc-dependencies: "<pvc-list>"` - JSON list of PVC dependencies

### TypedWork Naming Convention

TypedWork resources for pods will use the following naming pattern:
- **Name**: `pod-<pod-name>-<hash>` where hash is generated from pod namespace and name
- **Namespace**: `karmada-es-<cluster-name>` (execution space for target cluster)

### PVC Dependency Tracking

PVC dependencies will be tracked using:
- Annotation on TypedWork with list of required PVCs
- Validation before pod TypedWork creation
- Coordination with PVC binding controller

## Error Handling

### Error Categories

1. **Cluster Readiness Errors**
   - Cluster not found or not ready
   - Requeue with exponential backoff
   - Record warning events

2. **TypedWork Creation Errors**
   - API server connectivity issues
   - Resource conflicts
   - Validation failures
   - Logged with appropriate context and requeued

3. **PVC Dependency Errors**
   - Missing PVC TypedWork resources
   - PVC synchronization failures
   - Delay pod creation until dependencies are ready

4. **Cleanup Errors**
   - Orphaned TypedWork resources
   - Finalizer management issues
   - Graceful handling with retry logic

### Error Propagation Strategy

1. **Event Recording**
   - Success events for completed operations
   - Warning events for retryable errors
   - Error events for permanent failures

2. **Status Updates**
   - Monitor TypedWork status conditions
   - Propagate errors back to pod events
   - Handle TypedWork execution failures

3. **Retry Logic**
   - Exponential backoff for transient errors
   - Circuit breaker for persistent failures
   - Maximum retry limits

## Testing Strategy

### Unit Tests

1. **Controller Logic Tests**
   - Test TypedWork creation from pod resources
   - Verify proper metadata and label assignment
   - Test error handling and retry logic
   - Mock cluster client interactions

2. **Helper Function Tests**
   - Test `CreateOrUpdatePodTypedWork` function
   - Verify TypedWork querying functions
   - Test cleanup and deletion functions
   - Test pod manifest preparation

3. **PVC Dependency Tests**
   - Test PVC dependency validation
   - Verify coordination with PVC controller
   - Test dependency cleanup scenarios

### Integration Tests

1. **End-to-End Flow Tests**
   - Test complete pod to TypedWork to member cluster flow
   - Verify TypedWork execution results in correct member cluster state
   - Test pod lifecycle scenarios (create, update, delete)

2. **Multi-Cluster Tests**
   - Test pod migration between clusters
   - Verify cleanup of orphaned TypedWork resources
   - Test cluster failure scenarios

3. **PVC Integration Tests**
   - Test pods with PVC dependencies
   - Verify proper ordering of TypedWork creation
   - Test shared PVC scenarios

### Performance Tests

1. **Scale Tests**
   - Test with large numbers of pods
   - Verify TypedWork creation performance
   - Monitor resource usage and memory consumption

2. **Latency Tests**
   - Compare latency with direct manipulation approach
   - Measure TypedWork processing time
   - Verify acceptable performance characteristics

## Implementation Phases

### Phase 1: Core Infrastructure
- Implement TypedWork helper functions in `pkg/util/helper/`
- Add pod-specific TypedWork creation and management functions
- Create unit tests for helper functions
- Set up controller structure and basic reconciliation

### Phase 2: Controller Implementation
- Implement `PodBindingController` with TypedWork operations
- Add finalizer management and cleanup logic
- Implement error handling and event recording
- Add PVC dependency coordination

### Phase 3: Integration and Testing
- Integration testing with TypedWork execution controller
- End-to-end testing of pod operations
- Performance testing and optimization
- Multi-cluster scenario testing

### Phase 4: Documentation and Deployment
- Update documentation and examples
- Create migration guides
- Performance benchmarking
- Production readiness validation

## Monitoring and Observability

### Metrics

1. **Operation Metrics**
   - TypedWork creation/update/deletion counts for pods
   - Error rates and retry counts
   - Processing latency metrics
   - PVC dependency resolution time

2. **Resource Metrics**
   - Number of active TypedWork resources
   - Memory usage for TypedWork caching
   - API server request rates

### Logging

1. **Structured Logging**
   - TypedWork operations with context
   - Error conditions with stack traces
   - Performance metrics and timing
   - PVC dependency resolution logs

2. **Debug Information**
   - TypedWork manifest contents (at debug level)
   - Cluster selection decisions
   - Finalizer management operations

### Events

1. **Success Events**
   - TypedWork creation success
   - Pod synchronization completion
   - Cleanup operation success

2. **Warning Events**
   - Cluster readiness issues
   - PVC dependency delays
   - Retry operations

3. **Error Events**
   - TypedWork creation failures
   - Permanent synchronization errors
   - Cleanup failures

## Security Considerations

### RBAC Requirements

The controller will need the following permissions:

1. **Core Resources**
   - `pods`: get, list, watch, update, patch (for finalizers and events)
   - `persistentvolumeclaims`: get, list, watch (for dependency checking)

2. **Karmada Resources**
   - `typedworks`: create, get, list, watch, update, patch, delete
   - `clusters`: get, list, watch

3. **Events**
   - `events`: create, patch

### Resource Isolation

1. **Namespace Isolation**
   - TypedWork resources created in cluster-specific execution namespaces
   - Proper RBAC boundaries between clusters
   - Secure handling of pod manifests

2. **Cluster Access**
   - Secure cluster client creation
   - Proper authentication and authorization
   - Network policy considerations

## Performance Considerations

### Resource Usage

1. **Memory Management**
   - Efficient TypedWork caching strategies
   - Proper cleanup of completed resources
   - Optimized informer usage

2. **API Server Load**
   - Batch operations where possible
   - Efficient label selector queries
   - Rate limiting for TypedWork operations

3. **Network Usage**
   - Minimize cross-cluster API calls
   - Efficient status synchronization
   - Proper connection pooling

### Scalability

1. **High Pod Count**
   - Efficient TypedWork creation batching
   - Optimized reconciliation loops
   - Proper work queue management

2. **Multi-Cluster Scale**
   - Efficient cluster client management
   - Optimized TypedWork distribution
   - Proper resource cleanup strategies

### Caching Strategy

1. **Informer Optimization**
   - Leverage existing informer caches
   - Efficient event filtering
   - Proper cache synchronization

2. **TypedWork Caching**
   - Cache frequently accessed TypedWork resources
   - Implement proper cache invalidation
   - Optimize label selector queries

## Migration Strategy

### Coexistence Period

During the transition period, both controllers may need to coexist:

1. **Feature Flag**
   - Environment variable to enable new controller
   - Gradual rollout capability
   - Rollback mechanism

2. **Resource Identification**
   - Clear labeling to identify which controller manages which pods
   - Avoid conflicts between controllers
   - Proper cleanup during migration

### Data Migration

1. **Existing Pods**
   - Identify pods managed by old controller
   - Create corresponding TypedWork resources
   - Ensure no disruption to running workloads

2. **Cleanup**
   - Remove old controller resources
   - Clean up deprecated code paths
   - Update documentation and examples