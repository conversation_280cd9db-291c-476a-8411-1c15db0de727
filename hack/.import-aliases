{"k8s.io/api/admissionregistration/v1": "admissionregistrationv1", "k8s.io/api/admissionregistration/v1beta1": "admissionregistrationv1beta1", "k8s.io/api/admission/v1beta1": "admissionv1beta1", "k8s.io/api/admission/v1": "admissionv1", "k8s.io/api/apps/v1": "appsv1", "k8s.io/api/apps/v1beta1": "appsv1beta1", "k8s.io/api/apps/v1beta2": "appsv1beta2", "k8s.io/api/authentication/v1": "authenticationv1", "k8s.io/api/authentication/v1beta1": "authenticationv1beta1", "k8s.io/api/authorization/v1": "authorizationv1", "k8s.io/api/authorization/v1beta1": "authorizationv1beta1", "k8s.io/api/autoscaling/v1": "autoscalingv1", "k8s.io/api/autoscaling/v2": "autoscalingv2", "k8s.io/api/batch/v1": "batchv1", "k8s.io/api/batch/v1beta1": "batchv1beta1", "k8s.io/api/certificates/v1beta1": "certificatesv1beta1", "k8s.io/api/coordination/v1": "coordinationv1", "k8s.io/api/coordination/v1beta1": "coordinationv1beta1", "k8s.io/api/core/v1": "corev1", "k8s.io/api/discovery/v1": "discoveryv1", "k8s.io/api/events/v1": "eventsv1", "k8s.io/api/events/v1beta1": "eventsv1beta1", "k8s.io/api/extensions/v1beta1": "extensionsv1beta1", "k8s.io/api/imagepolicy/v1alpha1": "imagepolicyv1alpha1", "k8s.io/api/networking/v1": "networkingv1", "k8s.io/api/networking/v1beta1": "networkingv1beta1", "k8s.io/api/node/v1alpha1": "nodev1alpha1", "k8s.io/api/node/v1beta1": "nodev1beta1", "k8s.io/api/node/v1": "nodev1", "k8s.io/api/policy/v1": "policyv1", "k8s.io/api/policy/v1beta1": "policyv1beta1", "k8s.io/api/rbac/v1": "rbacv1", "k8s.io/api/rbac/v1alpha1": "rbacv1alpha1", "k8s.io/api/rbac/v1beta1": "rbacv1beta1", "k8s.io/api/scheduling/v1": "schedulingv1", "k8s.io/api/scheduling/v1alpha1": "schedulingv1alpha1", "k8s.io/api/scheduling/v1beta1": "schedulingv1beta1", "k8s.io/api/storage/v1": "storagev1", "k8s.io/api/storage/v1alpha1": "storagev1alpha1", "k8s.io/api/storage/v1beta1": "storagev1beta1", "k8s.io/apimachinery/pkg/api/errors": "apierrors", "k8s.io/apimachinery/pkg/apis/meta/v1": "metav1", "k8s.io/kubelet/apis/stats/v1alpha1": "kubeletstatsv1alpha1", "k8s.io/kubelet/pkg/apis/deviceplugin/v1alpha": "kubeletdevicepluginv1alpha", "k8s.io/kubelet/pkg/apis/deviceplugin/v1beta1": "kubeletdevicepluginv1beta1", "k8s.io/kubelet/pkg/apis/pluginregistration/v1": "kubeletpluginregistrationv1", "k8s.io/kubelet/pkg/apis/pluginregistration/v1alpha1": "kubeletpluginregistrationv1alpha1", "k8s.io/kubelet/pkg/apis/pluginregistration/v1beta1": "kubeletpluginregistrationv1beta1", "k8s.io/kubelet/pkg/apis/podresources/v1alpha1": "kubeletpodresourcesv1alpha1", "sigs.k8s.io/mcs-api/pkg/apis/v1alpha1": "mcsv1alpha1", "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1": "clusterv1alpha1", "github.com/karmada-io/karmada/pkg/apis/config/v1alpha1": "configv1alpha1", "github.com/karmada-io/karmada/pkg/apis/networking/v1alpha1": "networkingv1alpha1", "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1": "policyv1alpha1", "github.com/karmada-io/karmada/pkg/apis/search/v1alpha1": "searchv1alpha1", "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1": "workv1alpha1", "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2": "workv1alpha2", "github.com/karmada-io/karmada/operator/pkg/apis/operator/v1alpha1": "operatorv1alpha1"}