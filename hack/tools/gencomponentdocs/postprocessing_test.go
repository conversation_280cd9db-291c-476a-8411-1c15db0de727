package main

import "testing"

func TestCleanupForInclude(t *testing.T) {
	var tests = []struct {
		name                       string
		markdown, expectedMarkdown string
	}{
		{
			name: "add the title and auto-generated tag",
			markdown: "line 1\n" +
				"line 2\n" +
				"line 3\n",
			expectedMarkdown: "---\n" +
				"title: line 1\n" +
				"---\n" +
				"line 2\n" +
				"line 3\n" +
				"###### Auto generated by [spf13/cobra script in Karmada](https://github.com/karmada-io/karmada/tree/master/hack/tools/gencomponentdocs)",
		},
		{
			name: "remove see also",
			markdown: "line 1\n" +
				"line 2\n" +
				"line 3\n" +
				"### SEE ALSO\n" +
				"line 3",
			expectedMarkdown: "---\n" +
				"title: line 1\n" +
				"---\n" +
				"line 2\n" +
				"line 3\n" +
				"###### Auto generated by [spf13/cobra script in Karmada](https://github.com/karmada-io/karmada/tree/master/hack/tools/gencomponentdocs)",
		},
	}
	for _, rt := range tests {
		actual := cleanupForInclude(rt.markdown)
		if actual != rt.expectedMarkdown {
			t.Errorf(
				"failed cleanupForInclude:\n\texpected: %s\n\t  actual: %s",
				rt.expectedMarkdown,
				actual,
			)
		}
	}
}
