# Call-Graph and Data-Flow Diagram for `TypedWorkController`

## Call-Graph

- **Reconcile(ctx context.Context, req controllerruntime.Request)**
  - Entry point for reconciling TypedWork
  - **Handles**: Check if resource exists, cluster readiness, workload deletion, manages state

  - **syncTypedWork(clusterName string, typedWork *workv1alpha1.TypedWork)**
    - Responsible for synchronizing the TypedWork to member clusters.
    - **Path**: Handles syncing success and failure with metric recording and event creation.

  - **ensureWorkloadDeleted(clusterName string, typedWork *workv1alpha1.TypedWork)**
    - Ensures deletion of workloads from clusters.
    - **Path**: Confirms deletion through checks and retry strategies.

## Data-Flow Diagram

- **State to Decision Flow**:
  - When `Reconcile` is triggered, it decides based on the state of `TypedWork`.
  - **Cluster Readiness**: `IsClusterReady` checks ensure actions only proceed if the cluster is prepared.
  - **Manifest Operations**: Handles unmarshalling and applying manifests, logging successes, and handling errors.

- **Shared State**:
  - Uses shared utilities across modules to fetch state and operate on resources (e.g., `names`, `helper`, `objectwatcher`)

## Key Operations and Shared State

- **Logging**: Utilizes `klog` extensively for tracking state changes and errors.
- **Metrics**: Prometheus metrics for counting successes, failures, and operational durations.
- **Error Patterns**: Utilizes structured requeue strategies for handling errors gracefully.
