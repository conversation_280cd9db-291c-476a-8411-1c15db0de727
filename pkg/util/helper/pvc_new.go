package helper

// host pv 和 member pv 的备份和同步

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	kubeclientset "k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"

	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/kmap"
)

func EnsurePvcBackup(ctx context.Context, c client.Client, clusterName string, controlPlaneClient, memberClusterClient kubeclientset.Interface, typedWork *workv1alpha1.TypedWork, virtualPvc *corev1.PersistentVolumeClaim) error {
	objRef := typedWork.Spec.ObjectReference
	if !IsPvc(objRef) {
		return nil
	}
	if meta.IsStatusConditionTrue(typedWork.Status.Conditions, "PvBackup") {
		klog.Infof("pvc %s/%s has already been backed up with condition true, skip", virtualPvc.Namespace, virtualPvc.Name)
		return nil
	}

	if !virtualPvc.GetDeletionTimestamp().IsZero() {
		klog.Infof("PVC %s/%s is being deleted, skip backup", virtualPvc.Namespace, virtualPvc.Name)
		return nil
	}

	condition, err := EnsurePvBackedUpIfNeeded(ctx, clusterName, controlPlaneClient, memberClusterClient, virtualPvc)
	if err != nil {
		klog.Warningf("Failed to ensure pv backed up if needed for pvc %s/%s in cluster %s, err: %v", virtualPvc.Namespace, virtualPvc.Name, clusterName, err)
	}

	err = retry.RetryOnConflict(retry.DefaultRetry, func() (err error) {
		curTwk := &workv1alpha1.TypedWork{}
		if err := c.Get(context.TODO(), client.ObjectKey{Namespace: typedWork.Namespace, Name: typedWork.Name}, curTwk); err != nil {
			// stop processing as the typedwork object has been removed, assume it's a normal delete operation.
			if apierrors.IsNotFound(err) {
				return nil
			}

			klog.Errorf("Failed to get TypedWork from cache: %v", err)
			return err
		}
		typedWorkCopy := curTwk.DeepCopy()
		util.SetStatusConditionNew(&typedWorkCopy.Status.Conditions, condition)
		if reflect.DeepEqual(typedWorkCopy.Status, typedWork.Status) {
			return nil
		}
		return c.Status().Update(context.TODO(), typedWorkCopy)
	})
	if err != nil {
		klog.Errorf("Failed to update typedwork %s/%s: %v", typedWork.Namespace, typedWork.Name, err)
		return err
	}
	return nil
}

func NeedBackupPv(ctx context.Context, virtualPvc *corev1.PersistentVolumeClaim, clusterName string) (bool, error) {
	if util.GetLabelValue(virtualPvc.GetLabels(), workv1alpha1.TypedWorkPvMemberMigrateEnabledLabel) != "true" {
		return false, nil
	}
	if virtualPvc.Spec.VolumeName != "" {
		return false, nil
	}
	return true, nil
}

func readyToBackupPv(ctx context.Context, memberPvc *corev1.PersistentVolumeClaim) (bool, string) {
	if memberPvc.Spec.VolumeName == "" {
		return false, "pvc volume name is empty"
	}
	if memberPvc.Status.Phase != corev1.ClaimBound {
		return false, "pvc is not bound"
	}
	return true, ""
}

func backupMemberPv(ctx context.Context, clusterName string, controlPlaneClient, memberClusterClient kubeclientset.Interface, memberPvc *corev1.PersistentVolumeClaim) error {
	pvName := memberPvc.Spec.VolumeName
	hostPv, err := controlPlaneClient.CoreV1().PersistentVolumes().Get(ctx, memberPvc.Spec.VolumeName, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return fmt.Errorf("failed to get pv %s from host cluster: %v", pvName, err)
	}
	if hostPv != nil {
		_ = markVirtualPvBound(ctx, controlPlaneClient, hostPv)
		return nil
	}

	memberPv, err := memberClusterClient.CoreV1().PersistentVolumes().Get(ctx, pvName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get pv %s from member cluster: %v", pvName, err)
	}
	hostPv = newVirtualPv(clusterName, memberPvc, memberPv)
	_, err = controlPlaneClient.CoreV1().PersistentVolumes().Create(ctx, hostPv, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			klog.V(5).Infof(fmt.Sprintf("PV %s already exists in host cluster", pvName))
			return nil
		}
		return fmt.Errorf("failed to create pv %s in host cluster: %v", pvName, err)
	}
	klog.V(3).Infof(fmt.Sprintf("Successfully created pv %s in host cluster", pvName))
	_ = markVirtualPvBound(ctx, controlPlaneClient, hostPv)
	return nil
}

func markVirtualPvBound(ctx context.Context, controlPlaneClient kubeclientset.Interface, vpv *corev1.PersistentVolume) error {
	if vpv.Status.Phase == corev1.VolumeBound {
		return nil
	}
	vpv.Status.Phase = corev1.VolumeBound
	_, err := controlPlaneClient.CoreV1().PersistentVolumes().UpdateStatus(ctx, vpv, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("failed to update pv %s status in host cluster: %v", vpv.Name, err)
	}
	return nil
}

func newVirtualPv(clusterName string, virtualPvc *corev1.PersistentVolumeClaim, memberPv *corev1.PersistentVolume) *corev1.PersistentVolume {
	hostPv := memberPv.DeepCopy()
	TrimObjectMeta(&hostPv.ObjectMeta)
	hostPv.Finalizers = nil
	hostPv.Spec.ClaimRef = &corev1.ObjectReference{
		Name:       virtualPvc.Name,
		Namespace:  virtualPvc.Namespace,
		APIVersion: corev1.SchemeGroupVersion.String(),
		Kind:       "PersistentVolumeClaim",
		UID:        virtualPvc.UID,
	}
	hostPv.Labels = kmap.Union(hostPv.Labels, map[string]string{
		workv1alpha2.WorkPvBackupClusterLabel: clusterName,
	})
	return hostPv
}

// bindPvcVolumeNameInHostCluster 在host集群中创建或更新PVC
func bindPvcVolumeNameInHostCluster(ctx context.Context, controlPlaneClient kubeclientset.Interface, hostPvc *corev1.PersistentVolumeClaim, pvName string) error {
	// 如果volumeName已经是目标值，无需更新
	if hostPvc.Spec.VolumeName == pvName {
		return nil
	}

	// 使用JSON patch方式更新volumeName，避免并发冲突
	patchData := map[string]interface{}{
		"spec": map[string]interface{}{
			"volumeName": pvName,
		},
	}

	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		return fmt.Errorf("failed to marshal patch data: %v", err)
	}

	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		_, err := controlPlaneClient.CoreV1().PersistentVolumeClaims(hostPvc.Namespace).Patch(
			ctx,
			hostPvc.Name,
			types.MergePatchType,
			patchBytes,
			metav1.PatchOptions{},
		)
		if err != nil {
			return err
		}
		klog.Infof("Successfully updated PVC %s in host cluster with pv name %s", hostPvc.Name, pvName)
		return nil
	})
}

// EnsurePvBackedUpIfNeeded 在 pvc 状态反向同步时执行备份校验与执行，返回是否执行过备份以及原因
// 该方法仅做“是否需要备份”的快速判断与执行，保证在状态反射路径上轻量可靠
func EnsurePvBackedUpIfNeeded(ctx context.Context, clusterName string, controlPlaneClient, memberClusterClient kubeclientset.Interface, virtualPvc *corev1.PersistentVolumeClaim) (metav1.Condition, error) {
	condition := metav1.Condition{
		Type:   "PvBackup",
		Status: metav1.ConditionFalse,
	}

	if util.GetLabelValue(virtualPvc.GetLabels(), workv1alpha1.TypedWorkPvMemberMigrateEnabledLabel) != "true" {
		condition.Reason = "PvMemberMigrateDisabled"
		if reason := util.GetAnnotationValue(virtualPvc.GetAnnotations(), workv1alpha1.TypedWorkPvMemberMigrateReasonAnnotation); reason != "" {
			condition.Message = reason
		}
		return condition, nil
	}

	condition.Reason = "PvMemberMigrateEnabled"

	memberPvc, err := memberClusterClient.CoreV1().PersistentVolumeClaims(virtualPvc.Namespace).Get(ctx, virtualPvc.Name, metav1.GetOptions{})
	if err != nil {
		condition.Message = fmt.Sprintf("failed to get pvc %s in member cluster: %v", virtualPvc.Name, err)
		return condition, err
	}

	if ready, reason := readyToBackupPv(ctx, memberPvc); !ready {
		condition.Message = reason
		return condition, nil
	}

	if err := backupMemberPv(ctx, clusterName, controlPlaneClient, memberClusterClient, memberPvc); err != nil {
		condition.Message = fmt.Sprintf("failed to backup pv %s in member cluster: %v", memberPvc.Spec.VolumeName, err)
		return condition, err
	}

	if err := bindPvcVolumeNameInHostCluster(ctx, controlPlaneClient, virtualPvc, memberPvc.Spec.VolumeName); err != nil {
		condition.Message = fmt.Sprintf("backup success but failed to bind pvc %s with pv %s in host cluster: %v", virtualPvc.Name, memberPvc.Spec.VolumeName, err)
		return condition, err
	}

	condition.Status = metav1.ConditionTrue
	condition.Message = fmt.Sprintf("pv %s backed up in host cluster and bound to pvc %s", memberPvc.Spec.VolumeName, virtualPvc.Name)
	return condition, nil
}
