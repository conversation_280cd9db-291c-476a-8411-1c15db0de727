package helper

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"

	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/kmap"
)

func GetMemerPv(desired *corev1.PersistentVolume, clusterName string) *corev1.PersistentVolume {
	mPv := desired.DeepCopy()
	delete(mPv.Labels, workv1alpha2.WorkBindingClusterLabel)
	util.RecordManagedAnnotations(desired)
	util.RecordManagedLabels(desired)
	util.MergeAnnotation(mPv, workv1alpha2.ResourceTemplateUIDAnnotation, string(desired.GetUID()))

	mPv.Labels = kmap.Union(mPv.Labels, map[string]string{
		workv1alpha2.ResourceBindingUIDLabel: string(desired.GetUID()),
		util.ManagedByKarmadaLabel:           util.ManagedByKarmadaLabelValue,
	})
	return TrimPv(mPv)
}

func GetUpdatedPv(observed, desired *corev1.PersistentVolume, clusterName string) *corev1.PersistentVolume {
	desired = GetMemerPv(desired, clusterName)
	desired = retainClusterFields(desired, observed)

	ret := observed.DeepCopy()
	ret.Annotations = desired.Annotations
	ret.Labels = desired.Labels
	return ret
}

func TrimPv(pvCopy *corev1.PersistentVolume) *corev1.PersistentVolume {
	TrimObjectMeta(&pvCopy.ObjectMeta)
	pvCopy.Status = corev1.PersistentVolumeStatus{}
	pvCopy.SetFinalizers(nil)
	pvCopy.Spec.ClaimRef = nil
	return pvCopy
}

func DeleteMemberPvc(ctx context.Context, clusterName string, cc *util.ClusterClient, pvcName types.NamespacedName, volumeName string, retain bool) error {
	if retain {
		klog.V(3).Infof("Retaining PV data %s in member cluster %s", volumeName, clusterName)
		if err := updatePVReclaimPolicy(ctx, clusterName, volumeName, cc); err != nil {
			return err
		}
	}

	if err := deletePVCFromMemberCluster(ctx, clusterName, cc, pvcName); err != nil {
		return err
	}

	if retain {
		// If retain is true, pv will not deleted automatically, so we need to delete it manually.
		if err := deletePVFromMemberCluster(ctx, clusterName, cc, volumeName); err != nil {
			return err
		}
	}

	klog.V(3).Infof("Successfully cleaned PVC %s/%s from member cluster %s", pvcName.Namespace, pvcName.Name, clusterName)
	return nil
}

func deletePVCFromMemberCluster(ctx context.Context, clusterName string, clusterClient *util.ClusterClient, pvc types.NamespacedName) error {
	klog.V(3).Infof("Deleting PVC %s/%s from member cluster %s", pvc.Namespace, pvc.Name, clusterName)

	err := clusterClient.KubeClient.CoreV1().PersistentVolumeClaims(pvc.Namespace).Delete(ctx, pvc.Name, metav1.DeleteOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.V(4).Infof("PVC %s/%s already deleted from member cluster %s", pvc.Namespace, pvc.Name, clusterName)
			return nil
		}
		return fmt.Errorf("failed to delete PVC from member cluster: %v", err)
	}

	klog.V(3).Infof("Successfully deleted PVC %s/%s from member cluster %s", pvc.Namespace, pvc.Name, clusterName)
	return nil
}

func deletePVFromMemberCluster(ctx context.Context, clusterName string, clusterClient *util.ClusterClient, pvName string) error {
	klog.V(3).Infof("Deleting PV %s from member cluster %s", pvName, clusterName)

	err := clusterClient.KubeClient.CoreV1().PersistentVolumes().Delete(ctx, pvName, metav1.DeleteOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.V(4).Infof("PV %s already deleted from member cluster %s", pvName, clusterName)
			return nil
		}
		return fmt.Errorf("failed to delete PV from member cluster: %v", err)
	}

	klog.V(3).Infof("Successfully deleted PV %s from member cluster %s", pvName, clusterName)
	return nil
}

// updatePVReclaimPolicy deletes a PV from member cluster while preserving data.
func updatePVReclaimPolicy(ctx context.Context, clusterName string, volumeName string, clusterClient *util.ClusterClient) error {
	klog.V(3).Infof("Deleting PV %s from member cluster %s (preserving data)", volumeName, clusterName)

	// Get the current PV to check its reclaim policy
	currentPV, err := clusterClient.KubeClient.CoreV1().PersistentVolumes().Get(ctx, volumeName, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.V(4).Infof("PV %s already deleted from member cluster %s", volumeName, clusterName)
			return nil
		}
		return fmt.Errorf("failed to get PV %s from member cluster: %v", volumeName, err)
	}

	// If reclaim policy is Delete, temporarily change it to Retain to preserve data
	originalReclaimPolicy := currentPV.Spec.PersistentVolumeReclaimPolicy
	if originalReclaimPolicy == corev1.PersistentVolumeReclaimDelete {
		currentPV.Spec.PersistentVolumeReclaimPolicy = corev1.PersistentVolumeReclaimRetain
		currentPV.Spec.ClaimRef = nil
		if _, err := clusterClient.KubeClient.CoreV1().PersistentVolumes().Update(ctx, currentPV, metav1.UpdateOptions{}); err != nil {
			return fmt.Errorf("failed to update PV reclaim policy to Retain in cluster %s: %v", clusterName, err)
		}
		klog.V(3).Infof("Temporarily changed PV %s reclaim policy to Retain in cluster %s to preserve data", volumeName, clusterName)
	}
	return nil
}

// filterSystemLabels filters out system-managed labels that shouldn't be compared
func filterSystemLabels(labels map[string]string) map[string]string {
	if labels == nil {
		return nil
	}

	filtered := make(map[string]string)
	for key, value := range labels {
		// Skip system labels that are managed by Kubernetes or Karmada
		if strings.HasPrefix(key, "kubernetes.io/") ||
			strings.HasPrefix(key, "k8s.io/") ||
			key == "pv.kubernetes.io/bind-completed" ||
			key == "pv.kubernetes.io/bound-by-controller" {
			continue
		}
		filtered[key] = value
	}
	return filtered
}

// filterSystemAnnotations filters out system-managed annotations that shouldn't be compared
func filterSystemAnnotations(annotations map[string]string) map[string]string {
	if annotations == nil {
		return nil
	}

	filtered := make(map[string]string)
	for key, value := range annotations {
		// Skip system annotations that are managed by Kubernetes or Karmada
		if strings.HasPrefix(key, "kubernetes.io/") ||
			strings.HasPrefix(key, "k8s.io/") ||
			strings.HasPrefix(key, "pv.kubernetes.io/") ||
			strings.HasPrefix(key, "volume.beta.kubernetes.io/") ||
			strings.HasPrefix(key, "volume.kubernetes.io/") ||
			key == "kubectl.kubernetes.io/last-applied-configuration" {
			continue
		}
		filtered[key] = value
	}
	return filtered
}

func systemAnnotation(key string) bool {
	return strings.HasPrefix(key, "kubernetes.io/") ||
		strings.HasPrefix(key, "k8s.io/") ||
		strings.HasPrefix(key, "pv.kubernetes.io/") ||
		strings.HasPrefix(key, "volume.beta.kubernetes.io/") ||
		strings.HasPrefix(key, "volume.kubernetes.io/") ||
		key == "kubectl.kubernetes.io/last-applied-configuration"
}
