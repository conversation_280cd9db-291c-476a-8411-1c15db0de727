package status

import (
	"context"
	"fmt"
	"reflect"
	"time"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/events"
	"github.com/karmada-io/karmada/pkg/resourceinterpreter"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/fedinformer"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/genericmanager"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/keys"
	"github.com/karmada-io/karmada/pkg/util/helper"
	"github.com/karmada-io/karmada/pkg/util/names"
	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
	"github.com/karmada-io/karmada/pkg/util/restmapper"
)

// TypedWorkStatusControllerName is the controller name that will be used when reporting events.
const TypedWorkStatusControllerName = "typedwork-status-controller"

// TypedWorkStatusController is to sync status of TypedWork.
type TypedWorkStatusController struct {
	client.Client              // used to operate TypedWork resources.
	EventRecorder              record.EventRecorder
	RESTMapper                 meta.RESTMapper
	ControlPlaneClient         kubernetes.Interface
	InformerManager            genericmanager.MultiClusterInformerManager
	ControlPlanInformerManager genericmanager.SingleClusterInformerManager
	DynamicClient              dynamic.Interface
	eventHandler               cache.ResourceEventHandler // eventHandler knows how to handle events from the member cluster.
	StopChan                   <-chan struct{}
	worker                     util.AsyncWorker // worker process resources periodic from rateLimitingQueue.
	// ConcurrentTypedWorkStatusSyncs is the number of TypedWork status that are allowed to sync concurrently.
	ConcurrentTypedWorkStatusSyncs int
	ObjectWatcher                  objectwatcher.ObjectWatcher
	PredicateFunc                  predicate.Predicate
	ClusterDynamicClientSetFunc    func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)
	ClusterClientFunc              func(clusterName string, client client.Client) (*util.ClusterClient, error)
	ClusterCacheSyncTimeout        metav1.Duration
	RateLimiterOptions             ratelimiterflag.Options
	ResourceInterpreter            resourceinterpreter.ResourceInterpreter
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
// The Controller will requeue the Request to be processed again if an error is non-nil or
// Result.Requeue is true, otherwise upon completion it will remove the work from the queue.
func (c *TypedWorkStatusController) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling status of TypedWork %s.", req.NamespacedName.String())

	typedWork := &workv1alpha1.TypedWork{}
	if err := c.Client.Get(ctx, req.NamespacedName, typedWork); err != nil {
		// The resource may no longer exist, in which case we stop processing.
		if apierrors.IsNotFound(err) {
			return controllerruntime.Result{}, nil
		}

		return controllerruntime.Result{Requeue: true}, err
	}

	//if !typedWork.DeletionTimestamp.IsZero() {
	//	return controllerruntime.Result{}, nil
	//}

	if !helper.IsTypedResourceApplied(&typedWork.Status) {
		return controllerruntime.Result{}, nil
	}

	clusterName, err := names.GetClusterName(typedWork.GetNamespace())
	if err != nil {
		klog.Errorf("Failed to get member cluster name by %s. Error: %v.", typedWork.GetNamespace(), err)
		return controllerruntime.Result{Requeue: true}, err
	}

	cluster, err := util.GetCluster(c.Client, clusterName)
	if err != nil {
		klog.Errorf("Failed to the get given member cluster %s", clusterName)
		return controllerruntime.Result{Requeue: true}, err
	}

	if !util.IsClusterReady(&cluster.Status) {
		klog.Errorf("Stop sync typedwork(%s/%s) for cluster(%s) as cluster not ready.", typedWork.Namespace, typedWork.Name, cluster.Name)
		return controllerruntime.Result{Requeue: true}, fmt.Errorf("cluster(%s) not ready", cluster.Name)
	}

	return c.buildResourceInformers(cluster, typedWork)
}

// buildResourceInformers builds informer dynamically for managed resources in member cluster.
// The created informer watches resource change and then sync to the relevant TypedWork object.
func (c *TypedWorkStatusController) buildResourceInformers(cluster *clusterv1alpha1.Cluster, typedWork *workv1alpha1.TypedWork) (controllerruntime.Result, error) {
	err := c.registerInformersAndStart(cluster, typedWork)
	if err != nil {
		klog.Errorf("Failed to register informer for TypedWork %s/%s. Error: %v.", typedWork.GetNamespace(), typedWork.GetName(), err)
		return controllerruntime.Result{Requeue: true}, err
	}
	return controllerruntime.Result{}, nil
}

// getEventHandler return callback function that knows how to handle events from the member cluster.
func (c *TypedWorkStatusController) getEventHandler() cache.ResourceEventHandler {
	if c.eventHandler == nil {
		c.eventHandler = fedinformer.NewHandlerOnAllEvents(c.worker.Enqueue)
	}
	return c.eventHandler
}

// RunWorkQueue initializes worker and run it, worker will process resource asynchronously.
func (c *TypedWorkStatusController) RunWorkQueue() {
	workerOptions := util.Options{
		Name:          "typedwork-status",
		KeyFunc:       generateTypedWorkKey,
		ReconcileFunc: c.syncTypedWorkStatus,
	}
	c.worker = util.NewAsyncWorker(workerOptions)
	c.worker.Run(c.ConcurrentTypedWorkStatusSyncs, c.StopChan)
}

// generateTypedWorkKey generates a key from obj, the key contains cluster, GVK, namespace and name.
func generateTypedWorkKey(obj interface{}) (util.QueueKey, error) {
	resource := obj.(*unstructured.Unstructured)
	cluster, err := getClusterNameFromTypedWorkLabel(resource)
	if err != nil {
		return nil, err
	}
	// return a nil key when the obj not managed by Karmada, which will be discarded before putting to queue.
	if cluster == "" {
		return nil, nil
	}

	return keys.FederatedKeyFunc(cluster, obj)
}

// getClusterNameFromTypedWorkLabel gets cluster name from typedwork label, if label not exist, means resource is not created by karmada.
func getClusterNameFromTypedWorkLabel(resource *unstructured.Unstructured) (string, error) {
	typedWorkNamespace := util.GetLabelValue(resource.GetLabels(), workv1alpha1.TypedWorkNamespaceLabel)
	if len(typedWorkNamespace) == 0 {
		klog.V(4).Infof("Ignore resource(%s/%s/%s) which not managed by karmada", resource.GetKind(), resource.GetNamespace(), resource.GetName())
		return "", nil
	}

	cluster, err := names.GetClusterName(typedWorkNamespace)
	if err != nil {
		klog.Errorf("Failed to get cluster name from typedwork namespace: %s, error: %v.", typedWorkNamespace, err)
		return "", err
	}
	return cluster, nil
}

// syncTypedWorkStatus will collect status of object referencing by key and update to typedwork which holds the object.
func (c *TypedWorkStatusController) syncTypedWorkStatus(key util.QueueKey) error {
	fedKey, ok := key.(keys.FederatedKey)
	if !ok {
		klog.Errorf("Failed to sync status as invalid key: %v", key)
		return fmt.Errorf("invalid key")
	}

	observedObj, err := helper.GetObjectFromCache(c.RESTMapper, c.InformerManager, fedKey)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return c.handleDeleteEvent(fedKey)
		}
		return err
	}

	typedWorkNamespace := util.GetLabelValue(observedObj.GetLabels(), workv1alpha1.TypedWorkNamespaceLabel)
	typedWorkName := util.GetLabelValue(observedObj.GetLabels(), workv1alpha1.TypedWorkNameLabel)
	if len(typedWorkNamespace) == 0 || len(typedWorkName) == 0 {
		klog.Infof("Ignore object(%s) which not managed by karmada.", fedKey.String())
		return nil
	}

	typedWorkObject := &workv1alpha1.TypedWork{}
	if err := c.Client.Get(context.TODO(), client.ObjectKey{Namespace: typedWorkNamespace, Name: typedWorkName}, typedWorkObject); err != nil {
		// Stop processing if resource no longer exist.
		if apierrors.IsNotFound(err) {
			return nil
		}
		klog.Errorf("Failed to get TypedWork(%s/%s) from cache: %v", typedWorkNamespace, typedWorkName, err)
		return err
	}

	if util.GetLabelValue(observedObj.GetLabels(), workv1alpha2.WorkUIDLabel) != string(typedWorkObject.GetUID()) {
		klog.Errorf("Ignore object(%s) as it not belong to TypedWork(%s/%s)", fedKey.String(), typedWorkNamespace, typedWorkName)
		return nil
	}

	klog.Infof("reflecting %s(%s/%s) status to TypedWork(%s/%s)", observedObj.GetKind(), observedObj.GetNamespace(), observedObj.GetName(), typedWorkNamespace, typedWorkName)
	statusRaw, err := c.reflectStatus(typedWorkObject, observedObj)
	if err != nil {
		klog.Errorf("Failed to reflect status to TypedWork(%s/%s): %v", typedWorkNamespace, typedWorkName, err)
		return err
	}
	return c.UpdateResourceStatus(fedKey.Cluster, typedWorkObject, statusRaw)
}

// UpdateResourceStatus 使用提供的状态更新 host 集群中的资源状态
func (c *TypedWorkStatusController) UpdateResourceStatus(clusterName string, tw *workv1alpha1.TypedWork, status *runtime.RawExtension) error {
	if status == nil {
		return nil
	}
	objRef := tw.Spec.ObjectReference
	gvr, err := restmapper.GetGroupVersionResource(c.RESTMapper, schema.FromAPIVersionAndKind(objRef.APIVersion, objRef.Kind))
	if err != nil {
		klog.Errorf("Failed to get GVR from GVK(%s/%s), Error: %v", objRef.APIVersion, objRef.Kind, err)
		return err
	}

	resource, err := helper.FetchResourceTemplateByGvr(c.DynamicClient, c.ControlPlanInformerManager, gvr, types.NamespacedName{
		Namespace: objRef.Namespace,
		Name:      objRef.Name,
	})
	if err != nil {
		if apierrors.IsNotFound(err) {
			// It might happen when the resource template has been removed but the garbage collector hasn't removed
			// the ResourceBinding which dependent on resource template.
			// So, just return without retry(requeue) would save unnecessary loop.
			return nil
		}
		klog.Errorf("Failed to fetch workload for typedWork object(%s/%s). Error: %v",
			objRef.Namespace, objRef.Name, err)
		return err
	}

	if helper.IsPvc(objRef) {
		_ = c.handlePvcBackupAndStatusSync(context.Background(), clusterName, tw, resource)
	}

	// 解析 RawExtension 中的状态数据
	var statusData map[string]interface{}
	if err := json.Unmarshal(status.Raw, &statusData); err != nil {
		klog.Warningf("Failed to unmarshal status data for %T(%s/%s): %v", gvr, objRef.Namespace, objRef.Name, err)
		return fmt.Errorf("failed to unmarshal status data: %w", err)
	}

	if helper.IsCorev1Pod(objRef) {
		// Filter Pod container statuses if needed to align with virtual pod spec
		statusData = c.filterPodStatusIfNeeded(objRef, resource, statusData)
	}

	// Set the status field
	newObj := resource.DeepCopy()
	newObj.Object["status"] = statusData

	if reflect.DeepEqual(resource, newObj) {
		klog.V(4).Infof("Ignore update resource(%s/%s/%s) status as up to date.", gvr, objRef.Namespace, objRef.Name)
		return nil
	}
	_, err = c.DynamicClient.Resource(gvr).Namespace(newObj.GetNamespace()).UpdateStatus(context.TODO(), newObj, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("Failed to update status of %s(%s/%s) in host cluster, err is %v", objRef.Kind, newObj.GetNamespace(), newObj.GetName(), err)
		return fmt.Errorf("failed to update resource status in host cluster: %w", err)
	}
	klog.Infof("Updated status of %s(%s/%s) in host cluster with status from cluster: %s", objRef.Kind, newObj.GetNamespace(), newObj.GetName(), clusterName)
	return nil
}

// filterPodStatusIfNeeded filters Pod status to only include containers defined in virtual pod spec.
// Returns the filtered status data for Pod resources, or the original data for non-Pod resources.
func (c *TypedWorkStatusController) filterPodStatusIfNeeded(objRef workv1alpha1.TypedObjectReference, resource *unstructured.Unstructured, statusData map[string]interface{}) map[string]interface{} {
	if !helper.IsCorev1Pod(objRef) {
		return statusData
	}
	// Convert resource to Pod for spec extraction
	virtualPod := &corev1.Pod{}
	if err := helper.ConvertToTypedObject(resource.Object, virtualPod); err != nil {
		klog.Errorf("Failed to convert resource to Pod for %s/%s: %v", objRef.Namespace, objRef.Name, err)
		return statusData
	}
	// Convert status map to PodStatus struct using runtime converter (more efficient than JSON)
	memberPodStatus := &corev1.PodStatus{}
	if err := helper.ConvertToTypedObject(statusData, memberPodStatus); err != nil {
		klog.Errorf("Failed to convert status data to PodStatus for %s/%s: %v", objRef.Namespace, objRef.Name, err)
		return statusData
	}
	// Use existing helper function to filter pod status
	filteredStatus := helper.FilterPodStatusBySpec(virtualPod, memberPodStatus)
	// Convert filtered status back to map using runtime converter (more efficient than JSON)
	filteredStatusMap, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&filteredStatus)
	if err != nil {
		klog.Errorf("Failed to convert filtered PodStatus to map for %s/%s: %v", objRef.Namespace, objRef.Name, err)
		return statusData
	}
	klog.V(4).Infof("Successfully filtered Pod status for %s/%s", objRef.Namespace, objRef.Name)
	return filteredStatusMap
}

func (c *TypedWorkStatusController) handleDeleteEvent(key keys.FederatedKey) error {
	executionSpace := names.GenerateExecutionSpaceName(key.Cluster)

	// Given the workload might has been deleted from informer cache, so that we can't get typedwork object by it's label,
	// we have to get typedwork by naming rule as the typedwork's name is generated by the workload's kind, name and namespace.
	typedWorkName := names.GenerateWorkName(key.Kind, key.Name, key.Namespace)
	typedWork := &workv1alpha1.TypedWork{}
	if err := c.Client.Get(context.TODO(), client.ObjectKey{Namespace: executionSpace, Name: typedWorkName}, typedWork); err != nil {
		// stop processing as the typedwork object has been removed, assume it's a normal delete operation.
		if apierrors.IsNotFound(err) {
			return nil
		}

		klog.Errorf("Failed to get TypedWork from cache: %v", err)
		return err
	}

	// stop processing as the typedwork object being deleting.
	if !typedWork.DeletionTimestamp.IsZero() {
		return nil
	}

	return c.recreateResourceIfNeeded(typedWork, key)
}

func (c *TypedWorkStatusController) recreateResourceIfNeeded(typedWork *workv1alpha1.TypedWork, workloadKey keys.FederatedKey) error {
	if typedWork.Spec.Workload.Manifest == nil {
		return nil
	}

	manifest := &unstructured.Unstructured{}
	if err := manifest.UnmarshalJSON(typedWork.Spec.Workload.Manifest.Raw); err != nil {
		return err
	}

	desiredGVK := schema.FromAPIVersionAndKind(manifest.GetAPIVersion(), manifest.GetKind())
	if reflect.DeepEqual(desiredGVK, workloadKey.GroupVersionKind()) &&
		manifest.GetNamespace() == workloadKey.Namespace &&
		manifest.GetName() == workloadKey.Name {
		klog.Infof("recreating %s", workloadKey.String())
		return c.ObjectWatcher.Create(workloadKey.Cluster, manifest)
	}
	return nil
}

// reflectStatus grabs cluster object's running status then updates to its owner object(TypedWork).
func (c *TypedWorkStatusController) reflectStatus(typedWork *workv1alpha1.TypedWork, clusterObj *unstructured.Unstructured) (*runtime.RawExtension, error) {
	statusRaw, err := c.ResourceInterpreter.ReflectStatus(clusterObj)
	if err != nil {
		klog.Errorf("Failed to reflect status for object(%s/%s/%s) with resourceInterpreter.",
			clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), err)
		c.EventRecorder.Eventf(typedWork, corev1.EventTypeWarning, events.EventReasonReflectStatusFailed, "Reflect status for object(%s/%s/%s) failed, err: %s.", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), err.Error())
		return nil, err
	}
	c.EventRecorder.Eventf(typedWork, corev1.EventTypeNormal, events.EventReasonReflectStatusSucceed, "Reflect status for object(%s/%s/%s) succeed.", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName())

	if statusRaw == nil {
		return nil, nil
	}

	var resourceHealth workv1alpha1.ResourceHealth
	// When an unregistered resource kind is requested with the ResourceInterpreter,
	// the interpreter will return an error, we treat its health status as Unknown.
	healthy, err := c.ResourceInterpreter.InterpretHealth(clusterObj)
	if err != nil {
		resourceHealth = workv1alpha1.ResourceUnknown
		c.EventRecorder.Eventf(typedWork, corev1.EventTypeWarning, events.EventReasonInterpretHealthFailed, "Interpret health of object(%s/%s/%s) failed, err: %s.", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), err.Error())
	} else if healthy {
		resourceHealth = workv1alpha1.ResourceHealthy
		c.EventRecorder.Eventf(typedWork, corev1.EventTypeNormal, events.EventReasonInterpretHealthSucceed, "Interpret health of object(%s/%s/%s) as healthy.", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName())
	} else {
		resourceHealth = workv1alpha1.ResourceUnhealthy
		c.EventRecorder.Eventf(typedWork, corev1.EventTypeNormal, events.EventReasonInterpretHealthSucceed, "Interpret health of object(%s/%s/%s) as unhealthy.", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName())
	}

	manifestStatus := workv1alpha1.TypedManifestStatus{
		Status: statusRaw,
		Health: resourceHealth,
	}

	typedWorkCopy := typedWork.DeepCopy()
	err = retry.RetryOnConflict(retry.DefaultRetry, func() (err error) {
		if reflect.DeepEqual(typedWorkCopy.Status.ManifestStatus, &manifestStatus) {
			return nil
		}
		typedWorkCopy.Status.ManifestStatus = &manifestStatus
		updateErr := c.Status().Update(context.TODO(), typedWorkCopy)
		if updateErr == nil {
			return nil
		}

		updated := &workv1alpha1.TypedWork{}
		if err = c.Get(context.TODO(), client.ObjectKey{Namespace: typedWorkCopy.Namespace, Name: typedWorkCopy.Name}, updated); err == nil {
			typedWorkCopy = updated
		} else {
			klog.Errorf("Failed to get updated typedwork %s/%s: %v", typedWorkCopy.Namespace, typedWorkCopy.Name, err)
		}
		return updateErr
	})
	return statusRaw, err
}

func (c *TypedWorkStatusController) getRawManifest(typedWork *workv1alpha1.TypedWork, clusterObj *unstructured.Unstructured) (*unstructured.Unstructured, error) {
	if typedWork.Spec.Workload.Manifest == nil {
		return nil, fmt.Errorf("no manifest exist in TypedWork")
	}

	manifest := &unstructured.Unstructured{}
	if err := manifest.UnmarshalJSON(typedWork.Spec.Workload.Manifest.Raw); err != nil {
		return nil, err
	}

	if manifest.GetAPIVersion() == clusterObj.GetAPIVersion() &&
		manifest.GetKind() == clusterObj.GetKind() &&
		manifest.GetNamespace() == clusterObj.GetNamespace() &&
		manifest.GetName() == clusterObj.GetName() {
		return manifest, nil
	}

	return nil, fmt.Errorf("no such manifest exist")
}

// registerInformersAndStart builds informer manager for cluster if it doesn't exist, then constructs informers for gvr
// and start it.
func (c *TypedWorkStatusController) registerInformersAndStart(cluster *clusterv1alpha1.Cluster, typedWork *workv1alpha1.TypedWork) error {
	singleClusterInformerManager, err := c.getSingleClusterManager(cluster)
	if err != nil {
		return err
	}

	gvrTargets, err := c.getGVRsFromTypedWork(typedWork)
	if err != nil {
		return err
	}

	allSynced := true
	for gvr := range gvrTargets {
		if !singleClusterInformerManager.IsInformerSynced(gvr) || !singleClusterInformerManager.IsHandlerExist(gvr, c.getEventHandler()) {
			allSynced = false
			singleClusterInformerManager.ForResource(gvr, c.getEventHandler())
		}
	}
	if allSynced {
		return nil
	}

	c.InformerManager.Start(cluster.Name)

	if err := func() error {
		synced := c.InformerManager.WaitForCacheSyncWithTimeout(cluster.Name, c.ClusterCacheSyncTimeout.Duration)
		if synced == nil {
			return fmt.Errorf("no informerFactory for cluster %s exist", cluster.Name)
		}
		for gvr := range gvrTargets {
			if !synced[gvr] {
				return fmt.Errorf("informer for %s hasn't synced", gvr)
			}
		}
		return nil
	}(); err != nil {
		klog.Errorf("Failed to sync cache for cluster: %s, error: %v", cluster.Name, err)
		c.InformerManager.Stop(cluster.Name)
		return err
	}

	return nil
}

// getGVRsFromTypedWork traverses the manifest in typedwork to find groupVersionResource.
func (c *TypedWorkStatusController) getGVRsFromTypedWork(typedWork *workv1alpha1.TypedWork) (map[schema.GroupVersionResource]bool, error) {
	gvrTargets := map[schema.GroupVersionResource]bool{}
	if typedWork.Spec.Workload.Manifest == nil {
		return gvrTargets, nil
	}

	workload := &unstructured.Unstructured{}
	err := workload.UnmarshalJSON(typedWork.Spec.Workload.Manifest.Raw)
	if err != nil {
		klog.Errorf("Failed to unmarshal workload. Error: %v.", err)
		return nil, err
	}
	gvr, err := restmapper.GetGroupVersionResource(c.RESTMapper, workload.GroupVersionKind())
	if err != nil {
		klog.Errorf("Failed to get GVR from GVK for resource %s/%s. Error: %v.", workload.GetNamespace(), workload.GetName(), err)
		return nil, err
	}
	gvrTargets[gvr] = true
	return gvrTargets, nil
}

// getSingleClusterManager gets singleClusterInformerManager with clusterName.
// If manager is not exist, create it, otherwise gets it from map.
func (c *TypedWorkStatusController) getSingleClusterManager(cluster *clusterv1alpha1.Cluster) (genericmanager.SingleClusterInformerManager, error) {
	// TODO(chenxianpao): If cluster A is removed, then a new cluster that name also is A joins karmada,
	//  the cache in informer manager should be updated.
	singleClusterInformerManager := c.InformerManager.GetSingleClusterManager(cluster.Name)
	if singleClusterInformerManager == nil {
		dynamicClusterClient, err := c.ClusterDynamicClientSetFunc(cluster.Name, c.Client)
		if err != nil {
			klog.Errorf("Failed to build dynamic cluster client for cluster %s.", cluster.Name)
			return nil, err
		}
		singleClusterInformerManager = c.InformerManager.ForCluster(dynamicClusterClient.ClusterName, dynamicClusterClient.DynamicClientSet, 0)
	}
	return singleClusterInformerManager, nil
}

// SetupWithManager creates a controller and register to controller manager.
func (c *TypedWorkStatusController) SetupWithManager(mgr controllerruntime.Manager) error {
	return controllerruntime.NewControllerManagedBy(mgr).
		For(&workv1alpha1.TypedWork{}, builder.WithPredicates(c.PredicateFunc)).
		WithOptions(controller.Options{
			RateLimiter: ratelimiterflag.DefaultControllerRateLimiter(c.RateLimiterOptions),
		}).Complete(c)
}

// handlePvcBackupAndStatusSync 处理PVC备份和PV状态同步
// 这个函数封装了PVC相关的备份和状态同步逻辑，使主流程更清晰
func (c *TypedWorkStatusController) handlePvcBackupAndStatusSync(ctx context.Context, clusterName string, typedWork *workv1alpha1.TypedWork, virtualObj *unstructured.Unstructured) error {
	objRef := typedWork.Spec.ObjectReference
	if !helper.IsPvc(objRef) {
		return nil
	}

	virtualPvc := &corev1.PersistentVolumeClaim{}
	if err := helper.ConvertToTypedObject(virtualObj, virtualPvc); err != nil {
		klog.Errorf("Failed to convert resource object to pvc. Error: %v.", err)
		return err
	}

	clusterClient, err := c.ClusterClientFunc(clusterName, c.Client)
	if err != nil {
		klog.Errorf("Failed to get cluster client for cluster %s.", clusterName)
		return err
	}

	return helper.EnsurePvcBackup(ctx, c.Client, clusterName, c.ControlPlaneClient, clusterClient.KubeClient, typedWork, virtualPvc)
}

// handlePodDeletionEvent handles the special case when a Pod is deleted from member cluster
// If the TypedWork is in applied state, it will delete the corresponding virtual pod in host cluster
func (c *TypedWorkStatusController) handlePodDeletionEvent(typedWork *workv1alpha1.TypedWork, key keys.FederatedKey) error {
	if err := c.deleteVirtualPodInHostCluster(typedWork, key); err != nil {
		klog.Errorf("Failed to delete virtual pod in host cluster for %s: %v", key.String(), err)
		// Record event for debugging
		c.EventRecorder.Eventf(typedWork, corev1.EventTypeWarning, events.EventReasonSyncWorkloadFailed,
			"Failed to delete virtual pod %s/%s in host cluster: %v",
			typedWork.Spec.ObjectReference.Namespace, typedWork.Spec.ObjectReference.Name, err)
	}
	return nil
}

// deleteVirtualPodInHostCluster deletes the virtual pod in host cluster
func (c *TypedWorkStatusController) deleteVirtualPodInHostCluster(typedWork *workv1alpha1.TypedWork, key keys.FederatedKey) error {
	objRef := typedWork.Spec.ObjectReference

	// Create a context with timeout to avoid hanging
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()

	// Get the virtual pod from host cluster
	virtualPod := &corev1.Pod{}
	err := c.Client.Get(ctx, client.ObjectKey{Namespace: objRef.Namespace, Name: objRef.Name}, virtualPod)
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.V(4).Infof("Virtual pod %s/%s not found in host cluster, may have been already deleted", objRef.Namespace, objRef.Name)
			return nil
		}
		return fmt.Errorf("failed to get virtual pod %s/%s from host cluster: %w", objRef.Namespace, objRef.Name, err)
	}

	// Check if the virtual pod is already being deleted
	if !virtualPod.DeletionTimestamp.IsZero() {
		klog.V(4).Infof("virtual pod %s/%s is already being deleted, skip", objRef.Namespace, objRef.Name)
		return nil
	}

	// Check if the virtual pod is scheduled to the expected cluster
	expectedCluster := helper.GetMemberCluster(virtualPod)
	if expectedCluster != key.Cluster {
		klog.Warningf("Virtual pod %s/%s is scheduled to cluster %s, but deletion event is from cluster %s",
			objRef.Namespace, objRef.Name, expectedCluster, key.Cluster)
		return nil
	}

	// Delete the virtual pod
	klog.Infof("Deleting virtual pod %s/%s from host cluster due to member cluster pod deletion in cluster %s",
		objRef.Namespace, objRef.Name, key.Cluster)

	err = c.Client.Delete(ctx, virtualPod)
	if err != nil && !apierrors.IsNotFound(err) {
		return fmt.Errorf("failed to delete virtual pod %s/%s from host cluster: %w", objRef.Namespace, objRef.Name, err)
	}

	klog.Infof("Successfully deleted virtual pod %s/%s from host cluster", objRef.Namespace, objRef.Name)
	// Record successful event
	c.EventRecorder.Eventf(typedWork, corev1.EventTypeNormal, events.EventReasonSyncWorkloadSucceed,
		"Successfully deleted virtual pod %s/%s from host cluster due to member cluster pod deletion",
		objRef.Namespace, objRef.Name)
	return nil
}
