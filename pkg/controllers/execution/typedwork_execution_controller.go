package execution

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kubeclientset "k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/controllers/execution/resource"
	"github.com/karmada-io/karmada/pkg/events"
	"github.com/karmada-io/karmada/pkg/metrics"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/genericmanager"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/keys"
	"github.com/karmada-io/karmada/pkg/util/helper"
	"github.com/karmada-io/karmada/pkg/util/names"
	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
)

const (
	// TypedWorkControllerName is the controller name that will be used when reporting events.
	TypedWorkControllerName = "typedwork-execution-controller"
)

// TypedWorkController is to sync TypedWork.
type TypedWorkController struct {
	client.Client      // used to operate TypedWork resources.
	ControlPlaneClient kubeclientset.Interface
	EventRecorder      record.EventRecorder
	RESTMapper         meta.RESTMapper
	ObjectWatcher      objectwatcher.ObjectWatcher
	PredicateFunc      predicate.Predicate
	InformerManager    genericmanager.MultiClusterInformerManager
	RatelimiterOptions ratelimiterflag.Options

	ClusterClientFunc resource.ClusterClientFunc
	// resourceHandlers maps GVK to ResourceHandler
	resourceHandlers map[schema.GroupVersionKind]resource.ResourceHandler
	// defaultHandler handles resources that don't have specific handlers
	defaultHandler resource.ResourceHandler
}

// GetResourceHandler returns the resource handler for a specific GVK
func (c *TypedWorkController) GetResourceHandler(gvk schema.GroupVersionKind) (resource.ResourceHandler, bool) {
	handler, exists := c.resourceHandlers[gvk]
	return handler, exists
}

// getHandlerForWorkload returns the appropriate handler for the workload
// If a specific handler exists for the GVK, it returns that handler
// Otherwise, it returns the default handler
func (c *TypedWorkController) getHandlerForWorkload(workload *unstructured.Unstructured) resource.ResourceHandler {
	gvk := workload.GroupVersionKind()
	if handler, exists := c.resourceHandlers[gvk]; exists {
		klog.V(4).Infof("Using specific handler for resource %s/%s (GVK: %s)",
			workload.GetNamespace(), workload.GetName(), gvk.String())
		return handler
	}

	klog.V(4).Infof("Using default handler for resource %s/%s (GVK: %s)",
		workload.GetNamespace(), workload.GetName(), gvk.String())
	return c.defaultHandler
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
// The Controller will requeue the Request to be processed again if an error is non-nil or
// Result.Requeue is true, otherwise upon completion it will remove the work from the queue.
func (c *TypedWorkController) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling TypedWork %s", req.NamespacedName.String())

	typedWork := &workv1alpha1.TypedWork{}
	if err := c.Client.Get(ctx, req.NamespacedName, typedWork); err != nil {
		// The resource may no longer exist, in which case we stop processing.
		if apierrors.IsNotFound(err) {
			return controllerruntime.Result{}, nil
		}

		return controllerruntime.Result{Requeue: true}, err
	}

	clusterName, err := names.GetClusterName(typedWork.Namespace)
	if err != nil {
		klog.Errorf("Failed to get member cluster name for typedwork %s/%s", typedWork.Namespace, typedWork.Name)
		return controllerruntime.Result{Requeue: true}, err
	}

	cluster, err := util.GetCluster(c.Client, clusterName)
	if err != nil {
		klog.Errorf("Failed to get the given member cluster %s", clusterName)
		return controllerruntime.Result{Requeue: true}, err
	}

	if !typedWork.DeletionTimestamp.IsZero() {
		// Abort deleting workload if cluster is unready when unjoining cluster, otherwise the unjoin process will be failed.
		if util.IsClusterReady(&cluster.Status) {
			completed, err := c.ensureWorkloadDeleted(clusterName, typedWork)
			if err != nil {
				klog.Errorf("Failed to delete typedwork %v, namespace is %v, err is %v", typedWork.Name, typedWork.Namespace, err)
				return controllerruntime.Result{Requeue: true}, err
			}
			if !completed {
				klog.V(4).Infof("Workload for typedwork %s/%s still exists in cluster %s, requeue for deletion verification",
					typedWork.Namespace, typedWork.Name, clusterName)
				return controllerruntime.Result{RequeueAfter: time.Second * 5}, nil
			}
		} else if cluster.DeletionTimestamp.IsZero() { // cluster is unready, but not terminating
			return controllerruntime.Result{Requeue: true}, fmt.Errorf("cluster(%s) not ready", cluster.Name)
		}

		return c.removeFinalizer(typedWork)
	}

	if !util.IsClusterReady(&cluster.Status) {
		klog.Errorf("Stop sync typedwork(%s/%s) for cluster(%s) as cluster not ready.", typedWork.Namespace, typedWork.Name, cluster.Name)
		return controllerruntime.Result{Requeue: true}, fmt.Errorf("cluster(%s) not ready", cluster.Name)
	}

	return c.syncTypedWork(clusterName, typedWork)
}

// SetupWithManager creates a controller and register to controller manager.
func (c *TypedWorkController) SetupWithManager(mgr controllerruntime.Manager) error {
	c.resourceHandlers = resource.RegisterHandlers(mgr.GetClient(), c.ClusterClientFunc, c.ObjectWatcher, c.ControlPlaneClient)
	c.defaultHandler = resource.NewDefaultHandler(mgr.GetClient(), mgr.GetRESTMapper(), c.ObjectWatcher, genericmanager.GetInstance(), mgr.GetEventRecorderFor(TypedWorkControllerName))
	return controllerruntime.NewControllerManagedBy(mgr).
		For(&workv1alpha1.TypedWork{}, builder.WithPredicates(c.PredicateFunc)).
		WithEventFilter(predicate.GenerationChangedPredicate{}).
		WithOptions(controller.Options{
			RateLimiter: ratelimiterflag.DefaultControllerRateLimiter(c.RatelimiterOptions),
		}).
		Complete(c)
}
func (c *TypedWorkController) syncTypedWork(clusterName string, typedWork *workv1alpha1.TypedWork) (controllerruntime.Result, error) {
	start := time.Now()
	err := c.syncToClusters(clusterName, typedWork)
	metrics.ObserveSyncWorkloadLatency(err, start)
	if err != nil {
		msg := fmt.Sprintf("Failed to sync typedwork(%s) to cluster(%s): %v", typedWork.Name, clusterName, err)
		klog.Warning(msg)
		c.EventRecorder.Event(typedWork, corev1.EventTypeWarning, events.EventReasonSyncWorkloadFailed, msg)
		return controllerruntime.Result{Requeue: true}, err
	}
	msg := fmt.Sprintf("Sync typedwork (%s) to cluster(%s) successful.", typedWork.Name, clusterName)
	klog.V(4).Infof(msg)
	c.EventRecorder.Event(typedWork, corev1.EventTypeNormal, events.EventReasonSyncWorkloadSucceed, msg)
	return controllerruntime.Result{}, nil
}

// ensureWorkloadDeleted ensures the workload is deleted and verifies the deletion.
// Returns (deleted, error) where deleted indicates if the resource is confirmed deleted.
func (c *TypedWorkController) ensureWorkloadDeleted(clusterName string, typedWork *workv1alpha1.TypedWork) (bool, error) {
	if typedWork.Spec.Workload.Manifest == nil {
		return true, nil
	}

	workload := &unstructured.Unstructured{}
	err := workload.UnmarshalJSON(typedWork.Spec.Workload.Manifest.Raw)
	if err != nil {
		klog.Errorf("Failed to unmarshal workload, error is: %v", err)
		return false, err
	}

	fedKey, err := keys.FederatedKeyFunc(clusterName, workload)
	if err != nil {
		klog.Errorf("Failed to get FederatedKey %s, error: %v", workload.GetName(), err)
		return false, err
	}

	clusterObj, err := helper.GetObjectFromCache(c.RESTMapper, c.InformerManager, fedKey)
	if err != nil {
		if apierrors.IsNotFound(err) {
			// Resource not found, deletion is confirmed
			return true, nil
		}
		klog.Errorf("Failed to get resource %v from member cluster, err is %v ", workload.GetName(), err)
		return false, err
	}

	// Avoid deleting resources that not managed by karmada.
	if util.GetLabelValue(clusterObj.GetLabels(), workv1alpha1.TypedWorkNameLabel) != util.GetLabelValue(workload.GetLabels(), workv1alpha1.TypedWorkNameLabel) {
		klog.Infof("Abort deleting the resource(kind=%s, %s/%s) exists in cluster %v but not managed by karmada", clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), clusterName)
		// Resource exists but not managed by karmada, consider as deleted from karmada perspective
		return true, nil
	}

	// Check if resource has deletion timestamp, if so, wait for actual deletion
	if !clusterObj.GetDeletionTimestamp().IsZero() {
		klog.V(4).Infof("Resource(kind=%s, %s/%s) in cluster %s is being deleted, waiting for completion",
			clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), clusterName)
		return false, nil
	}

	// Try to delete the resource using appropriate handler
	err = c.deleteWithHandler(clusterName, typedWork, clusterObj)
	if err != nil {
		if apierrors.IsNotFound(err) {
			// Resource was deleted between our check and delete call
			return true, nil
		}
		klog.Errorf("Failed to delete resource in the given member cluster %v, err is %v", clusterName, err)
		return false, err
	}

	// After deletion request, verify the resource is actually being deleted
	// by checking if it now has a deletion timestamp
	clusterObj, err = helper.GetObjectFromCache(c.RESTMapper, c.InformerManager, fedKey)
	if err != nil {
		if apierrors.IsNotFound(err) {
			// Resource is deleted
			return true, nil
		}
		// Error getting resource, but deletion was initiated, requeue to verify later
		klog.V(4).Infof("Failed to verify deletion status for resource %s/%s in cluster %s, will requeue: %v",
			workload.GetNamespace(), workload.GetName(), clusterName, err)
		return false, nil
	}

	// Resource still exists, check if it has deletion timestamp
	if !clusterObj.GetDeletionTimestamp().IsZero() {
		klog.V(4).Infof("Resource(kind=%s, %s/%s) in cluster %s is being deleted, waiting for completion",
			clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), clusterName)
		return false, nil
	}

	// Resource still exists without deletion timestamp, something might be wrong
	klog.Warningf("Resource(kind=%s, %s/%s) in cluster %s still exists after deletion request",
		clusterObj.GetKind(), clusterObj.GetNamespace(), clusterObj.GetName(), clusterName)
	return false, nil
}

// removeFinalizer remove finalizer from the given TypedWork
func (c *TypedWorkController) removeFinalizer(typedWork *workv1alpha1.TypedWork) (controllerruntime.Result, error) {
	if !controllerutil.ContainsFinalizer(typedWork, util.ExecutionControllerFinalizer) {
		return controllerruntime.Result{}, nil
	}

	controllerutil.RemoveFinalizer(typedWork, util.ExecutionControllerFinalizer)
	err := c.Client.Update(context.TODO(), typedWork)
	if err != nil {
		return controllerruntime.Result{Requeue: true}, err
	}
	return controllerruntime.Result{}, nil
}

// syncToClusters ensures that the state of the given object is synchronized to member clusters.
func (c *TypedWorkController) syncToClusters(clusterName string, typedWork *workv1alpha1.TypedWork) error {
	if typedWork.Spec.Workload.Manifest == nil {
		return fmt.Errorf("typedwork %s/%s has no manifest", typedWork.Namespace, typedWork.Name)
	}

	workload := &unstructured.Unstructured{}
	err := workload.UnmarshalJSON(typedWork.Spec.Workload.Manifest.Raw)
	util.MergeLabel(workload, workv1alpha2.WorkUIDLabel, string(typedWork.UID))
	if err != nil {
		klog.Errorf("Failed to unmarshal workload, error is: %v", err)
		message := fmt.Sprintf("Failed to unmarshal manifest: %v", err)
		updateErr := c.updateAppliedCondition(typedWork, metav1.ConditionFalse, "AppliedFailed", message)
		if updateErr != nil {
			klog.Errorf("Failed to update applied status for given typedwork %v, namespace is %v, err is %v", typedWork.Name, typedWork.Namespace, updateErr)
			return updateErr
		}
		return err
	}

	_, err = c.createOrUpdateWithHandler(clusterName, workload)
	if err != nil {
		klog.Errorf("Failed to create or update resource(%v/%v) in the given member cluster %s, err is %v", workload.GetNamespace(), workload.GetName(), clusterName, err)
		message := fmt.Sprintf("Failed to apply manifest: %v", err)
		updateErr := c.updateAppliedCondition(typedWork, metav1.ConditionFalse, "AppliedFailed", message)
		if updateErr != nil {
			klog.Errorf("Failed to update applied status for given typedwork %v, namespace is %v, err is %v", typedWork.Name, typedWork.Namespace, updateErr)
			return updateErr
		}
		return err
	}

	err = c.updateAppliedCondition(typedWork, metav1.ConditionTrue, "AppliedSuccessful", "Manifest has been successfully applied")
	if err != nil {
		klog.Errorf("Failed to update applied status for given typedwork %v, namespace is %v, err is %v", typedWork.Name, typedWork.Namespace, err)
		return err
	}

	return nil
}

// createOrUpdateWithHandler uses the appropriate resource handler to create or update workload
func (c *TypedWorkController) createOrUpdateWithHandler(clusterName string, workload *unstructured.Unstructured) (controllerutil.OperationResult, error) {
	handler := c.getHandlerForWorkload(workload)

	// Convert workload to the appropriate type for the handler
	obj, err := handler.ConvertFromUnstructured(workload)
	if err != nil {
		klog.Errorf("Failed to convert workload %s/%s: %v", workload.GetNamespace(), workload.GetName(), err)
		return controllerutil.OperationResultNone, err
	}

	// Get federated key for cache lookup
	fedKey, err := keys.FederatedKeyFunc(clusterName, workload)
	if err != nil {
		klog.Errorf("Failed to get FederatedKey %s, error: %v", workload.GetName(), err)
		return controllerutil.OperationResultNone, err
	}

	// Try to get existing object from cache
	clusterObj, err := helper.GetObjectFromCache(c.RESTMapper, c.InformerManager, fedKey)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			klog.Errorf("Failed to get resource %v from member cluster, err is %v", workload.GetName(), err)
			return controllerutil.OperationResultNone, err
		}

		// Resource doesn't exist, create it
		err = handler.Create(clusterName, obj)
		if err != nil {
			klog.Errorf("Failed to create resource(%v/%v) in the given member cluster %s, err is %v",
				workload.GetNamespace(), workload.GetName(), clusterName, err)
			c.eventf(clusterObj, corev1.EventTypeWarning, events.EventReasonSyncWorkloadFailed, "Failed to create resource(%s) in member cluster(%s): %v", klog.KObj(workload), clusterName, err)

			return controllerutil.OperationResultNone, err
		}
		return controllerutil.OperationResultCreated, nil
	}

	// Resource exists, convert cluster object to handler type and update
	clusterTypedObj, err := handler.ConvertFromUnstructured(clusterObj)
	if err != nil {
		klog.Errorf("Failed to convert cluster object %s/%s: %v", clusterObj.GetNamespace(), clusterObj.GetName(), err)
		return controllerutil.OperationResultNone, err
	}

	err = handler.Update(clusterName, obj, clusterTypedObj)
	if err != nil {
		klog.Errorf("Failed to update resource in the given member cluster %s, err is %v", clusterName, err)
		c.eventf(clusterObj, corev1.EventTypeWarning, events.EventReasonSyncWorkloadFailed, "Failed to update resource(%s) in member cluster(%s): %v", klog.KObj(workload), clusterName, err)
		return controllerutil.OperationResultNone, err
	}
	return controllerutil.OperationResultUpdated, nil
}

// deleteWithHandler uses the appropriate resource handler to delete workload
func (c *TypedWorkController) deleteWithHandler(clusterName string, typedWork *workv1alpha1.TypedWork, workload *unstructured.Unstructured) error {
	handler := c.getHandlerForWorkload(workload)

	// Convert workload to the appropriate type for the handler
	obj, err := handler.ConvertFromUnstructured(workload)
	if err != nil {
		klog.Errorf("Failed to convert workload %s/%s: %v", workload.GetNamespace(), workload.GetName(), err)
		return err
	}

	return handler.Delete(clusterName, typedWork, obj)
}

func (c *TypedWorkController) tryCreateOrUpdateWorkload(clusterName string, workload *unstructured.Unstructured) (controllerutil.OperationResult, error) {
	fedKey, err := keys.FederatedKeyFunc(clusterName, workload)
	if err != nil {
		klog.Errorf("Failed to get FederatedKey %s, error: %v", workload.GetName(), err)
		return controllerutil.OperationResultNone, err
	}

	clusterObj, err := helper.GetObjectFromCache(c.RESTMapper, c.InformerManager, fedKey)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			klog.Errorf("Failed to get resource %v from member cluster, err is %v ", workload.GetName(), err)
			return controllerutil.OperationResultNone, err
		}
		err = c.ObjectWatcher.Create(clusterName, workload)
		if err != nil {
			klog.Errorf("Failed to create resource(%v/%v) in the given member cluster %s, err is %v", workload.GetNamespace(), workload.GetName(), clusterName, err)
			return controllerutil.OperationResultNone, err
		}
		return controllerutil.OperationResultCreated, nil
	}

	err = c.ObjectWatcher.Update(clusterName, workload, clusterObj)
	if err != nil {
		klog.Errorf("Failed to update resource in the given member cluster %s, err is %v", clusterName, err)
		return controllerutil.OperationResultNone, err
	}
	return controllerutil.OperationResultUpdated, nil
}

// updateAppliedCondition update the Applied condition for the given TypedWork
func (c *TypedWorkController) updateAppliedCondition(typedWork *workv1alpha1.TypedWork, status metav1.ConditionStatus, reason, message string) error {
	newTypedWorkAppliedCondition := metav1.Condition{
		Type:               workv1alpha1.TypedWorkApplied,
		Status:             status,
		Reason:             reason,
		Message:            message,
		LastTransitionTime: metav1.Now(),
	}

	return retry.RetryOnConflict(retry.DefaultRetry, func() (err error) {
		util.SetStatusConditionNew(&typedWork.Status.Conditions, newTypedWorkAppliedCondition)
		updateErr := c.Status().Update(context.TODO(), typedWork)
		if updateErr == nil {
			return nil
		}
		updated := &workv1alpha1.TypedWork{}
		if err = c.Get(context.TODO(), client.ObjectKey{Namespace: typedWork.Namespace, Name: typedWork.Name}, updated); err == nil {
			typedWork = updated
		} else {
			klog.Errorf("Failed to get updated typedwork %s/%s: %v", typedWork.Namespace, typedWork.Name, err)
		}
		return updateErr
	})
}

func (c *TypedWorkController) eventf(object *unstructured.Unstructured, eventType, reason, messageFmt string, args ...interface{}) {
	ref, err := helper.GenEventRef(object)
	if err != nil {
		klog.Errorf("ignore event(%s) as failed to build event reference for: kind=%s, %s due to %v", reason, object.GetKind(), klog.KObj(object), err)
		return
	}
	c.EventRecorder.Eventf(ref, eventType, reason, messageFmt, args...)
}
