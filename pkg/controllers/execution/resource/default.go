package resource

import (
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"

	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/genericmanager"
	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
)

// DefaultHandler implements ResourceHandler for generic resources
type DefaultHandler struct {
	client          client.Client
	restMapper      meta.RESTMapper
	watcher         objectwatcher.ObjectWatcher
	informerManager genericmanager.MultiClusterInformerManager
	eventRecorder   record.EventRecorder
}

// NewDefaultHandler creates a new DefaultHandler
func NewDefaultHandler(c client.Client, restMapper meta.RESTMapper, watcher objectwatcher.ObjectWatcher, 
	informerManager genericmanager.MultiClusterInformerManager, eventRecorder record.EventRecorder) ResourceHandler {
	return &DefaultHandler{
		client:          c,
		restMapper:      restMapper,
		watcher:         watcher,
		informerManager: informerManager,
		eventRecorder:   eventRecorder,
	}
}

func (h *DefaultHandler) Gvr() schema.GroupVersionResource {
	// Default handler doesn't have a specific GVR
	return schema.GroupVersionResource{}
}

func (h *DefaultHandler) ConvertFromUnstructured(obj *unstructured.Unstructured) (client.Object, error) {
	// For default handler, return the unstructured object as-is
	return obj, nil
}

func (h *DefaultHandler) Create(clusterName string, obj client.Object) error {
	workload := obj.(*unstructured.Unstructured)
	
	err := h.watcher.Create(clusterName, workload)
	if err != nil {
		klog.Errorf("Failed to create resource(%v/%v) in the given member cluster %s, err is %v", 
			workload.GetNamespace(), workload.GetName(), clusterName, err)
		return err
	}
	return nil
}

func (h *DefaultHandler) Update(clusterName string, desiredObj, clusterObj client.Object) error {
	workload := desiredObj.(*unstructured.Unstructured)
	clusterUnstructured := clusterObj.(*unstructured.Unstructured)
	
	err := h.watcher.Update(clusterName, workload, clusterUnstructured)
	if err != nil {
		klog.Errorf("Failed to update resource in the given member cluster %s, err is %v", clusterName, err)
		return err
	}
	return nil
}

func (h *DefaultHandler) Delete(clusterName string, typedWork *workv1alpha1.TypedWork, obj client.Object) error {
	workload := obj.(*unstructured.Unstructured)
	return h.watcher.Delete(clusterName, workload)
}