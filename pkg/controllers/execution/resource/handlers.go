package resource

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
	kubeclientset "k8s.io/client-go/kubernetes"
)

var (
	resourceHandlers = make(map[schema.GroupVersionKind]NewResourceHanlderFunc)
)

func init() {
	resourceHandlers[corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaim")] = NewPVCHandler
}

func RegisterHandlers(c client.Client, clusterClientFunc ClusterClientFunc, watcher objectwatcher.ObjectWatcher, controlPlaneClient kubeclientset.Interface) map[schema.GroupVersionKind]ResourceHandler {
	ret := make(map[schema.GroupVersionKind]ResourceHandler)
	for gvk, handler := range resourceHandlers {
		ret[gvk] = handler(c, clusterClientFunc, watcher, controlPlaneClient)
	}
	return ret
}
