package resource

import (
	kubeclientset "k8s.io/client-go/kubernetes"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"

	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
)

type ClusterClientFunc func(c client.Client, clusterName string) (*util.ClusterClient, error)

type NewResourceHanlderFunc func(c client.Client, clusterClientFunc ClusterClientFunc, watcher objectwatcher.ObjectWatcher, controlPlaneClient kubeclientset.Interface) ResourceHandler

// ResourceHandler defines the interface for handling different resource types
type ResourceHandler interface {
	Gvr() schema.GroupVersionResource		
	ConvertFromUnstructured(obj *unstructured.Unstructured) (client.Object, error)
	Create(clusterName string, obj client.Object) error
	Update(clusterName string, desiredObj, clusterObj client.Object) error
	Delete(clusterName string, typedWork *workv1alpha1.TypedWork, obj client.Object) error
}
