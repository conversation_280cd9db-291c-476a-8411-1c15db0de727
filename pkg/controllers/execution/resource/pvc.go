package resource

import (
	"context"
	"fmt"
	"reflect"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	kubeclientset "k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	workv1alpha1 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"

	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/fedinformer/typedmanager"
	"github.com/karmada-io/karmada/pkg/util/helper"
	"github.com/karmada-io/karmada/pkg/util/objectwatcher"
)

// PVCHandler implements ResourceHandler for PersistentVolumeClaim resources
type PVCHandler struct {
	clusterClientFunc  ClusterClientFunc
	client             client.Client
	watcher            objectwatcher.ObjectWatcher
	informerManager    typedmanager.MultiClusterInformerManager
	controlPlaneClient kubeclientset.Interface
}

// NewPVCHandler creates a new PVCHandler
func NewPVCHandler(c client.Client, clusterClientFunc ClusterClientFunc, watcher objectwatcher.ObjectWatcher, controlPlaneClient kubeclientset.Interface) ResourceHandler {
	return &PVCHandler{
		clusterClientFunc:  clusterClientFunc,
		client:             c,
		watcher:            watcher,
		informerManager:    typedmanager.GetInstance(),
		controlPlaneClient: controlPlaneClient,
	}
}

func (h *PVCHandler) Gvr() schema.GroupVersionResource {
	return typedmanager.PvcGVR
}

func (h *PVCHandler) ConvertFromUnstructured(obj *unstructured.Unstructured) (client.Object, error) {
	ret := &corev1.PersistentVolumeClaim{}
	if err := helper.ConvertToTypedObject(obj, ret); err != nil {
		return nil, err
	}
	return ret, nil
}

func (h *PVCHandler) Create(clusterName string, obj client.Object) error {
	pvc := obj.(*corev1.PersistentVolumeClaim)

	clusterClient, err := h.clusterClientFunc(h.client, clusterName)
	if err != nil {
		return err
	}

	if pvc.Spec.VolumeName != "" {
		_, err = h.syncDependentPv2Member(context.Background(), clusterName, clusterClient, pvc.Spec.VolumeName)
		if err != nil {
			return fmt.Errorf("failed to sync pv %s to member cluster: %v", pvc.Spec.VolumeName, err)
		}
	}
	// Convert PVC to unstructured for ObjectWatcher
	pvcUnstructured, err := helper.ToUnstructured(pvc)
	if err != nil {
		return fmt.Errorf("failed to convert PVC to unstructured: %v", err)
	}

	return h.watcher.Create(clusterName, pvcUnstructured)
}

func (h *PVCHandler) Update(clusterName string, desiredObj, clusterObj client.Object) error {
	pvc := desiredObj.(*corev1.PersistentVolumeClaim)

	clusterClient, err := h.clusterClientFunc(h.client, clusterName)
	if err != nil {
		return err
	}

	if pvc.Spec.VolumeName != "" {
		_, err = h.syncDependentPv2Member(context.Background(), clusterName, clusterClient, pvc.Spec.VolumeName)
		if err != nil {
			return fmt.Errorf("failed to sync pv %s to member cluster: %v", pvc.Spec.VolumeName, err)
		}
	}
	// Convert clusterObj to unstructured for ObjectWatcher
	clusterUnstructured, err := helper.ToUnstructured(clusterObj)
	if err != nil {
		return fmt.Errorf("failed to convert cluster object to unstructured: %v", err)
	}

	// Convert desiredObj to unstructured for ObjectWatcher
	desiredUnstructured, err := helper.ToUnstructured(desiredObj)
	if err != nil {
		return fmt.Errorf("failed to convert desired object to unstructured: %v", err)
	}

	return h.watcher.Update(clusterName, desiredUnstructured, clusterUnstructured)
}

// syncDependentPv2Member pv 只同步创建，暂不需要更新
func (h *PVCHandler) syncDependentPv2Member(ctx context.Context, clusterName string, cc *util.ClusterClient, pvName string) (controllerutil.OperationResult, error) {
	hostPv := &corev1.PersistentVolume{}
	if err := h.client.Get(ctx, types.NamespacedName{Name: pvName}, hostPv); err != nil {
		if apierrors.IsNotFound(err) {
			klog.Infof("PV %s not found in host cluster, will create in member cluster", pvName)
		} else {
			return controllerutil.OperationResultNone, fmt.Errorf("failed to get PV from host cluster: %v", err)
		}
	} else if !hostPv.GetDeletionTimestamp().IsZero() {
		klog.Infof("PV %s has been deleted in host cluster", pvName)
		return controllerutil.OperationResultNone, fmt.Errorf("hostPv %s has been deleted in host cluster", pvName)
	}

	if err := helper.EnsureBindingCluster(ctx, h.client, hostPv, clusterName); err != nil {
		return controllerutil.OperationResultNone, fmt.Errorf("failed to ensure binding cluster: %v", err)
	}

	observed, err := cc.KubeClient.CoreV1().PersistentVolumes().Get(ctx, pvName, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return controllerutil.OperationResultNone, fmt.Errorf("failed to get PV from member cluster: %v", err)
	}

	memberPv := helper.GetMemerPv(hostPv, clusterName)
	if apierrors.IsNotFound(err) {
		// PV不存在，需要创建
		if _, err := cc.KubeClient.CoreV1().PersistentVolumes().Create(ctx, memberPv, metav1.CreateOptions{}); err != nil {
			return controllerutil.OperationResultNone, fmt.Errorf("failed to create PV in member cluster: %v", err)
		}
		return controllerutil.OperationResultCreated, nil
	}

	updated := helper.GetUpdatedPv(observed, hostPv, clusterName)
	if !reflect.DeepEqual(observed.Labels, updated.Labels) || !reflect.DeepEqual(observed.Annotations, updated.Annotations) {
		if _, err := cc.KubeClient.CoreV1().PersistentVolumes().Update(ctx, updated, metav1.UpdateOptions{}); err != nil {
			return controllerutil.OperationResultNone, fmt.Errorf("failed to update PV in member cluster: %v", err)
		}
		return controllerutil.OperationResultUpdated, nil
	}
	return controllerutil.OperationResultNone, nil
}

func (h *PVCHandler) Delete(clusterName string, typedWork *workv1alpha1.TypedWork, obj client.Object) error {
	memberPvc := obj.(*corev1.PersistentVolumeClaim)
	clusterClient, err := h.clusterClientFunc(h.client, clusterName)
	if err != nil {
		return err
	}

	virtualPvcDeleted := false
	virtualPvc, err := h.controlPlaneClient.CoreV1().PersistentVolumeClaims(typedWork.Spec.ObjectReference.Namespace).Get(context.Background(), typedWork.Spec.ObjectReference.Name, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.Infof("PVC %s/%s not found in host cluster, will create in member cluster", typedWork.Spec.ObjectReference.Namespace, typedWork.Spec.ObjectReference.Name)
			virtualPvcDeleted = true
		}
		klog.Errorf("Failed to get PVC %s/%s in host cluster: %v", typedWork.Spec.ObjectReference.Namespace, typedWork.Spec.ObjectReference.Name, err)
		return err
	}

	virtualPvcDeleted = !virtualPvc.GetDeletionTimestamp().IsZero()
	if !virtualPvcDeleted && util.GetLabelValue(virtualPvc.Labels, workv1alpha2.WorkBindingClusterLabel) == clusterName {
		if err := helper.EnsurePvcBackup(context.Background(), h.client, clusterName, h.controlPlaneClient, clusterClient.KubeClient, typedWork, virtualPvc); err != nil {
			return err
		}
	}

	// 如果memberPvcDeleted为true，则不保留PV数据
	retain := !virtualPvcDeleted

	return helper.DeleteMemberPvc(context.Background(), clusterName, clusterClient, types.NamespacedName{
		Namespace: memberPvc.Namespace,
		Name:      memberPvc.Name,
	}, memberPvc.Spec.VolumeName, retain)
}
